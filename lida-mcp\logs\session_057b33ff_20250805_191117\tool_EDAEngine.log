[2025-08-05 19:12:47.992] [057b33ff] [0.000s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: basic
[2025-08-05 19:12:47.992] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: data_quality
[2025-08-05 19:12:47.992] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: advanced
[2025-08-05 19:12:47.992] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: advanced
[2025-08-05 19:12:47.992] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:12:47.997] [057b33ff] [0.005s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:12:47.997] [057b33ff] [0.005s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:12:47.997] [057b33ff] [0.006s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['basic', 'data_quality', 'advanced']
[2025-08-05 19:12:47.997] [057b33ff] [0.006s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: basic
[2025-08-05 19:12:48.046] [057b33ff] [0.055s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: basic
[2025-08-05 19:12:48.046] [057b33ff] [0.055s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: data_quality
[2025-08-05 19:12:48.083] [057b33ff] [0.092s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: data_quality
[2025-08-05 19:12:48.083] [057b33ff] [0.092s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: advanced
[2025-08-05 19:17:08.215] [057b33ff] [260.226s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Analysis 'advanced' failed: 'NoneType' object is not iterable
[2025-08-05 19:17:08.253] [057b33ff] [260.262s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: EDA analysis completed in 260.26 seconds
[2025-08-05 19:17:08.327] [057b33ff] [260.336s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: basic
[2025-08-05 19:17:08.327] [057b33ff] [260.336s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: data_quality
[2025-08-05 19:17:08.327] [057b33ff] [260.336s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: basic
[2025-08-05 19:17:08.328] [057b33ff] [260.336s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:17:08.342] [057b33ff] [260.350s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:17:08.342] [057b33ff] [260.351s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:17:08.342] [057b33ff] [260.351s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['basic', 'data_quality']
[2025-08-05 19:17:08.342] [057b33ff] [260.351s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: basic
[2025-08-05 19:17:08.399] [057b33ff] [260.408s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: basic
[2025-08-05 19:17:08.399] [057b33ff] [260.408s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: data_quality
[2025-08-05 19:17:08.431] [057b33ff] [260.440s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: data_quality
[2025-08-05 19:17:08.434] [057b33ff] [260.442s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: EDA analysis completed in 0.11 seconds
[2025-08-05 19:17:12.895] [057b33ff] [264.904s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 19:17:12.896] [057b33ff] [264.904s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 19:17:12.896] [057b33ff] [264.904s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:17:12.902] [057b33ff] [264.910s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:17:12.902] [057b33ff] [264.911s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:17:12.902] [057b33ff] [264.911s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 19:17:12.902] [057b33ff] [264.911s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: custom
[2025-08-05 19:17:26.734] [057b33ff] [278.743s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: custom
[2025-08-05 19:17:26.738] [057b33ff] [278.747s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: EDA analysis completed in 13.84 seconds
[2025-08-05 19:18:43.960] [057b33ff] [355.969s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 19:18:43.960] [057b33ff] [355.969s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 19:18:43.961] [057b33ff] [355.969s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:18:43.974] [057b33ff] [355.983s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:18:43.975] [057b33ff] [355.983s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:18:43.975] [057b33ff] [355.983s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 19:18:43.975] [057b33ff] [355.983s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: custom
[2025-08-05 19:18:51.817] [057b33ff] [363.825s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: custom
[2025-08-05 19:18:51.821] [057b33ff] [363.829s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: EDA analysis completed in 7.86 seconds
[2025-08-05 19:18:58.120] [057b33ff] [370.129s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 19:18:58.120] [057b33ff] [370.129s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 19:18:58.120] [057b33ff] [370.129s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:18:58.125] [057b33ff] [370.134s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:18:58.126] [057b33ff] [370.134s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:18:58.126] [057b33ff] [370.134s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 19:18:58.126] [057b33ff] [370.134s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: custom
[2025-08-05 19:19:03.465] [057b33ff] [375.474s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: custom
[2025-08-05 19:19:03.475] [057b33ff] [375.484s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: EDA analysis completed in 5.35 seconds
[2025-08-05 19:19:10.474] [057b33ff] [382.482s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 19:19:10.474] [057b33ff] [382.483s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 19:19:10.474] [057b33ff] [382.483s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:19:10.482] [057b33ff] [382.490s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:19:10.482] [057b33ff] [382.491s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:19:10.482] [057b33ff] [382.491s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 19:19:10.483] [057b33ff] [382.491s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: custom
[2025-08-05 19:19:17.009] [057b33ff] [389.017s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: custom
[2025-08-05 19:19:17.014] [057b33ff] [389.023s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: EDA analysis completed in 6.54 seconds
