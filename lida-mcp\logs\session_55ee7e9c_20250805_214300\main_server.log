[2025-08-05 21:43:04.621] [55ee7e9c] [0.001s] INFO in eda_mcp_server: EDA MCP Server session started - ID: 55ee7e9c
[2025-08-05 21:43:04.621] [55ee7e9c] [0.001s] INFO in eda_mcp_server: Log directory: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp\logs\session_55ee7e9c_20250805_214300
[2025-08-05 21:43:04.622] [55ee7e9c] [0.002s] INFO in eda_mcp_server: Initializing EDA MCP Server
[2025-08-05 21:43:04.622] [55ee7e9c] [0.002s] INFO in eda_mcp_server: Configuration loaded - endpoint: https://sivanithish-test.openai.azure.com/, deployment: gpt-4.1
[2025-08-05 21:43:04.623] [55ee7e9c] [0.003s] INFO in eda_mcp_server: Jina API key configured: jina_704d29e58ed547c...
[2025-08-05 21:43:04.624] [55ee7e9c] [0.004s] INFO in eda_mcp_server: Configuring Azure OpenAI client
[2025-08-05 21:43:05.358] [55ee7e9c] [0.737s] INFO in eda_mcp_server: Azure OpenAI configured with model: gpt-4.1
[2025-08-05 21:43:05.423] [55ee7e9c] [0.803s] INFO in eda_mcp_server: Starting EDA MCP server...
[2025-08-05 21:43:05.424] [55ee7e9c] [0.803s] INFO in eda_mcp_server: Version: 0.1.0
[2025-08-05 21:43:05.424] [55ee7e9c] [0.804s] INFO in eda_mcp_server: Model: gpt-4.1
[2025-08-05 21:43:05.424] [55ee7e9c] [0.804s] INFO in eda_mcp_server: Base path: Not set
[2025-08-05 21:43:05.425] [55ee7e9c] [0.805s] INFO in eda_mcp_server: Available tools: 1 tools registered
[2025-08-05 21:43:05.425] [55ee7e9c] [0.805s] INFO in eda_mcp_server: Python version: 3.13.2 (main, Mar 11 2025, 17:20:07) [MSC v.1943 64 bit (AMD64)]
[2025-08-05 21:43:05.426] [55ee7e9c] [0.806s] INFO in eda_mcp_server: Working directory: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp
[2025-08-05 21:43:05.426] [55ee7e9c] [0.806s] INFO in eda_mcp_server: AZURE_OPENAI_ENDPOINT: https://sivanithish-...
[2025-08-05 21:43:05.427] [55ee7e9c] [0.807s] INFO in eda_mcp_server: AZURE_OPENAI_DEPLOYMENT_NAME: gpt-4.1...
[2025-08-05 21:43:05.427] [55ee7e9c] [0.807s] INFO in eda_mcp_server: EDA_MCP_BASE_PATH: Not set
[2025-08-05 21:43:05.428] [55ee7e9c] [0.807s] INFO in eda_mcp_server: EDA MCP Server initialization complete
[2025-08-05 21:43:05.428] [55ee7e9c] [0.809s] INFO in eda_mcp_server: ============================================================
[2025-08-05 21:43:05.564] [55ee7e9c] [0.944s] INFO in eda_mcp_server: Starting tool: perform_eda
