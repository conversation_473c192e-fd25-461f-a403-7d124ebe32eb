[2025-08-05 19:27:12.208] [18ff7ebf] [0.005s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 19:27:12.213] [18ff7ebf] [0.010s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 19:27:12.213] [18ff7ebf] [0.010s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Request details - File: C:\Users\<USER>\Downloads\swtf_v1.csv, Goals: ['Analyze reference_date time series patterns, calculate forecast accuracy trends over time, detect seasonality in monthly data, and identify temporal correlations between different forecast types and actual orders']
[2025-08-05 19:27:12.215] [18ff7ebf] [0.012s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 1/6: Loading and validating data
[2025-08-05 19:27:12.215] [18ff7ebf] [0.012s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:27:12.216] [18ff7ebf] [0.013s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: File exists, size: 0.02 MB
[2025-08-05 19:27:12.251] [18ff7ebf] [0.048s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:27:12.251] [18ff7ebf] [0.048s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:27:12.251] [18ff7ebf] [0.048s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ Data loaded successfully: 174 rows, 19 columns
[2025-08-05 19:27:12.252] [18ff7ebf] [0.049s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Columns: ['reference_date', 'pc', 'profit_center_program', 'carline', 'month', 'lag_month', 'actual_order_quantity', 'forecast_percentile', 'sw_forecast_quantity', 'customer_forecast_quantity', 'prorated_derate', 'prorated_edi', 'edi', 'derate', 'total_weeks', 'in_scope_weeks', 'forecast_count', 'min_target_date', 'max_target_date']
[2025-08-05 19:27:12.259] [18ff7ebf] [0.056s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Data types: {'reference_date': dtype('O'), 'pc': dtype('O'), 'profit_center_program': dtype('O'), 'carline': dtype('O'), 'month': dtype('O'), 'lag_month': dtype('float64'), 'actual_order_quantity': dtype('float64'), 'forecast_percentile': dtype('O'), 'sw_forecast_quantity': dtype('float64'), 'customer_forecast_quantity': dtype('float64'), 'prorated_derate': dtype('int64'), 'prorated_edi': dtype('int64'), 'edi': dtype('int64'), 'derate': dtype('int64'), 'total_weeks': dtype('int64'), 'in_scope_weeks': dtype('int64'), 'forecast_count': dtype('float64'), 'min_target_date': dtype('O'), 'max_target_date': dtype('O')}
[2025-08-05 19:27:12.260] [18ff7ebf] [0.057s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 2/6: Applying sampling if needed
[2025-08-05 19:27:12.260] [18ff7ebf] [0.057s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 3/6: Selecting analyses to perform
[2025-08-05 19:27:12.260] [18ff7ebf] [0.057s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 19:27:12.260] [18ff7ebf] [0.057s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 4/6: Executing analyses
[2025-08-05 19:27:12.260] [18ff7ebf] [0.057s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing 1 analysis modules: ['custom']
[2025-08-05 19:27:12.260] [18ff7ebf] [0.057s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: [1/1] Starting analysis: custom
[2025-08-05 19:27:12.260] [18ff7ebf] [0.057s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Options for custom: {'target_columns': None, 'analysis_goals': ['Analyze reference_date time series patterns, calculate forecast accuracy trends over time, detect seasonality in monthly data, and identify temporal correlations between different forecast types and actual orders'], 'analysis_depth': 'comprehensive'}
[2025-08-05 19:27:47.769] [18ff7ebf] [35.566s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ [1/1] Completed analysis: custom in 35.51s
[2025-08-05 19:27:47.769] [18ff7ebf] [35.566s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result keys for custom: ['custom_analysis']
[2025-08-05 19:27:47.769] [18ff7ebf] [35.566s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Analysis execution completed. Successful modules: 1
[2025-08-05 19:27:47.770] [18ff7ebf] [35.567s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 5/6: Generating visualizations
[2025-08-05 19:27:47.770] [18ff7ebf] [35.567s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Visualization generation not yet implemented
[2025-08-05 19:27:47.777] [18ff7ebf] [35.574s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 6/6: Generating recommendations
[2025-08-05 19:27:47.777] [18ff7ebf] [35.574s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Creating final result
[2025-08-05 19:27:47.791] [18ff7ebf] [35.588s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ EDA analysis completed successfully in 35.58 seconds
[2025-08-05 19:27:47.791] [18ff7ebf] [35.588s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result contains 1 analysis modules
