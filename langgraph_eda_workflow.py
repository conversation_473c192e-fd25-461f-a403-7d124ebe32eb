"""
LangGraph-based EDA MCP Workflow with Time Series Analysis Focus

This script creates a LangGraph workflow that processes a dataset through the following stages:
1. Summarize the dataset
2. Generate analysis goals based on the summary
3. Perform custom time series analysis using the perform_eda tool with "custom" analysis_type
4. Generate search prompts from the analysis
5. Use the first search prompt with Jina DeepSearch
6. Verify the analysis results based on the research findings

The workflow uses MultiServerMCPClient to connect to the EDA MCP server and specifically 
performs custom time series analysis focusing on:
- Temporal patterns and trends identification
- Seasonality and cyclical behavior analysis
- Time-based correlations and autocorrelation
- Anomaly detection in time series data
- Trend decomposition and forecasting insights
- Date/time column distribution analysis
"""

import asyncio
import json
import os
from typing import TypedDict, Annotated, List, Dict, Any
from pathlib import Path
from dotenv import load_dotenv

from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.prebuilt import ToolNode, tools_condition
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

from langchain_google_genai import ChatGoogleGenerativeAI

# Load environment variables from .env file
load_dotenv()

# Initialize Gemini model
model = ChatGoogleGenerativeAI(
    model="gemini-2.0-flash",
    temperature=0.7,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)

class DataAnalysisState(TypedDict):
    """State for the data analysis workflow"""
    messages: Annotated[List, "The conversation messages"]
    dataset_path: str
    summary: str
    eda_analysis: str
    search_prompts: str
    research_results: str
    verification: str
    current_step: str
    errors: List[str]

class EdaWorkflow:
    """LangGraph workflow for EDA MCP data analysis pipeline"""
    
    def __init__(self, eda_server_path: str):
        """Initialize the workflow with EDA MCP server configuration"""
        self.eda_server_path = eda_server_path
        self.client = None
        self.tools = None
        self.graph = None
        
    async def initialize(self):
        """Initialize the MCP client and tools"""
        print("🔧 Initializing EDA MCP client...")
        
        try:
            # Configure the MCP client for EDA server - matching working client.py setup
            self.client = MultiServerMCPClient({
                "eda": {
                    "command": "uv",
                    "args": [
                        "--directory",
                        os.getenv("EDA_MCP_SERVER_PATH", "./lida-mcp"),
                        "run",
                        "eda-mcp"
                    ],
                    "transport": "stdio",
                    "env": {
                        "AZURE_OPENAI_API_KEY": os.getenv("AZURE_OPENAI_API_KEY"),
                        "AZURE_OPENAI_ENDPOINT": os.getenv("AZURE_OPENAI_ENDPOINT"),
                        "AZURE_OPENAI_DEPLOYMENT_NAME": os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
                        "AZURE_OPENAI_API_VERSION": os.getenv("AZURE_OPENAI_API_VERSION"),
                        "JINA_API_KEY": os.getenv("JINA_API_KEY"),
                        "LOCALAPPDATA": os.getenv("LOCALAPPDATA")
                    }
                }
            })
            
            print("✅ MCP client configured, getting tools...")
            # Get available tools
            self.tools = await self.client.get_tools()
            print(f"✅ Connected to EDA MCP server with {len(self.tools)} tools")
            
            # Build the workflow graph
            self._build_graph()
            
        except Exception as e:
            import traceback
            print(f"❌ Failed to initialize MCP client: {str(e)}")
            print(f"🔍 Full traceback:")
            print(traceback.format_exc())
            raise
        
    def _build_graph(self):
        """Build the LangGraph workflow"""
        print("🏗️ Building LangGraph workflow...")
        
        # Create the state graph
        builder = StateGraph(DataAnalysisState)
        
        # Add nodes for each step
        builder.add_node("start_analysis", self._start_analysis)
        builder.add_node("call_model", self._call_model)
        builder.add_node("tools", ToolNode(self.tools))
        builder.add_node("process_tool_results", self._process_tool_results)
        
        # Define the flow
        builder.add_edge(START, "start_analysis")
        builder.add_edge("start_analysis", "call_model")
        
        # Add conditional edges for tool usage
        builder.add_conditional_edges(
            "call_model",
            self._should_continue,
            {
                "tools": "tools",
                "end": END
            }
        )

        # Tools flow to result processing, then back to model
        builder.add_edge("tools", "process_tool_results")
        builder.add_edge("process_tool_results", "call_model")
        
        # Compile the graph
        self.graph = builder.compile()
        print("✅ LangGraph workflow built successfully")

    def _should_continue(self, state: DataAnalysisState):
        """Determine whether to continue with tools or end the workflow"""
        messages = state.get("messages", [])
        current_step = state.get("current_step", "start")

        # Check if the last message has tool calls
        if messages and hasattr(messages[-1], 'tool_calls') and messages[-1].tool_calls:
            return "tools"

        # Check if we've completed all steps
        if current_step == "completed" or current_step == "error":
            return "end"

        # Check if we have all required results
        has_summary = bool(state.get("summary"))
        has_eda = bool(state.get("eda_analysis"))
        has_search_prompts = bool(state.get("search_prompts"))
        has_research = bool(state.get("research_results"))
        has_verification = bool(state.get("verification"))

        # If we have all results, end the workflow
        if has_summary and has_eda and has_search_prompts and has_research and has_verification:
            return "end"

        # Otherwise, continue (but this shouldn't happen if workflow is designed correctly)
        return "end"
        
    def _start_analysis(self, state: DataAnalysisState):
        """Initialize the analysis workflow"""
        print(f"🚀 Starting analysis for dataset: {state['dataset_path']}")
        
        return {
            "messages": [
                SystemMessage(content="""You are an expert data analyst using EDA MCP tools to perform comprehensive data analysis.
                
Your workflow will proceed through these steps:
1. Summarize the dataset using summarize_dataset tool
2. Perform custom time series analysis using perform_eda tool with custom analysis type
3. Generate search prompts using generate_search_prompts tool
4. Research using jina_deepsearch_research tool with the first prompt
5. Verify and synthesize findings

IMPORTANT: When instructed to call a tool, you MUST call that tool with the exact parameters provided. Do not ask for clarification or additional information. The file paths and parameters will be provided in the instructions.

For step 2, you will perform a custom time series analysis that focuses on temporal patterns, trends, seasonality, and time-based insights. Always use the specific MCP tools available and provide detailed analysis at each step."""),
                HumanMessage(content=f"Please analyze the dataset at: {state['dataset_path']}")
            ],
            "current_step": "start",
            "errors": []
        }
    
    def _call_model(self, state: DataAnalysisState):
        """Call the model with tools for sequential analysis steps"""
        current_step = state.get("current_step", "start")
        print(f"📊 Current step: {current_step}")
        
        # Determine next step and create appropriate prompt
        if current_step == "start":
            prompt = f"Step 1: Use the summarize_dataset tool to analyze the dataset at {state['dataset_path']}. Provide a comprehensive summary of the data structure, content, and key insights."
            next_step = "summarizing"
            
        elif current_step == "summarizing":
            # Check if we have summary results from tool execution
            if not state.get('summary'):
                # Still waiting for summary tool results
                prompt = f"Continue with step 1: Use the summarize_dataset tool to analyze the dataset at {state['dataset_path']}."
                next_step = "summarizing"
            else:
                # Skip goal generation and go directly to EDA
                prompt = f"""Step 2: Now call the perform_eda tool to perform custom time series analysis on the dataset.

REQUIRED TOOL CALL: perform_eda
Parameters to use:
- file_path: "{state['dataset_path']}"
- analysis_type: "custom"
- analysis_depth: "comprehensive"
- analysis_goals: ["Perform comprehensive time series analysis on the dataset. Identify temporal patterns, trends, seasonality, and time-based correlations. If date/time columns exist, analyze their distribution and patterns. Generate insights about changes over time, detect anomalies, and provide time-based forecasts or recommendations. Include autocorrelation analysis, trend decomposition, and identification of periodic behaviors."]
- max_execution_time: 300

Call the tool now with these exact parameters.

Dataset summary: {state.get('summary', '')}"""
                next_step = "performing_eda"
            
        elif current_step == "performing_eda":
            if not state.get('eda_analysis'):
                prompt = f"""Continue with step 2: Call the perform_eda tool now.

REQUIRED TOOL CALL: perform_eda
Parameters to use:
- file_path: "{state['dataset_path']}"
- analysis_type: "custom"
- analysis_depth: "comprehensive"
- analysis_goals: ["Perform comprehensive time series analysis on the dataset. Identify temporal patterns, trends, seasonality, and time-based correlations. If date/time columns exist, analyze their distribution and patterns. Generate insights about changes over time, detect anomalies, and provide time-based forecasts or recommendations. Include autocorrelation analysis, trend decomposition, and identification of periodic behaviors."]
- max_execution_time: 300

Call the tool now with these exact parameters."""
                next_step = "performing_eda"
            else:
                prompt = f"Step 3: Based on the dataset analysis so far, use generate_search_prompts tool to create 5 intelligent search queries for external validation.\n\nAnalysis context:\nSummary: {state.get('summary', '')}\nTime Series EDA Analysis: {state.get('eda_analysis', '')}"
                next_step = "generating_search_prompts"
            
        elif current_step == "generating_search_prompts":
            if not state.get('search_prompts'):
                prompt = f"Continue with step 3: Use the generate_search_prompts tool based on the analysis so far."
                next_step = "generating_search_prompts"
            else:
                # Extract first search prompt
                prompts_text = state.get('search_prompts', '')
                first_prompt = self._extract_first_search_prompt(prompts_text)
                print(f"🔍 Extracted search prompt: '{first_prompt}'")
                prompt = f"Step 4: Use the jina_deepsearch_research tool with this search query to research external information. Use these parameters: query='{first_prompt}', reasoning_effort='low', stream=false, max_tokens=2000. The research may take 3-5 minutes to complete."
                next_step = "researching"
            
        elif current_step == "researching":
            if not state.get('research_results'):
                prompts_text = state.get('search_prompts', '')
                first_prompt = self._extract_first_search_prompt(prompts_text)
                print(f"🔍 Extracted search prompt (retry): '{first_prompt}'")
                prompt = f"Continue with step 4: Use the jina_deepsearch_research tool with these parameters: query='{first_prompt}', reasoning_effort='low', stream=false, max_tokens=2000. Wait patiently for the response as it may take 3-5 minutes."
                next_step = "researching"
            else:
                prompt = f"""Step 5: COMPREHENSIVE VERIFICATION AND SYNTHESIS

Based on all analysis components, provide detailed verification and synthesis of findings. Compare and validate results across all analysis stages:

ANALYSIS COMPONENTS TO SYNTHESIZE:
=================================

1. DATASET SUMMARY:
{state.get('summary', 'Not available')[:800]}

2. TIME SERIES EDA ANALYSIS:
{state.get('eda_analysis', 'Not available')[:1200]}

The EDA analysis above was specifically focused on time series patterns, temporal trends, seasonality analysis, and time-based insights.

3. SEARCH PROMPTS:
{state.get('search_prompts', 'Not available')[:400]}

4. EXTERNAL RESEARCH FINDINGS:
{state.get('research_results', 'Not available')[:1000]}

VERIFICATION TASKS:
==================
1. Validate if the time series EDA analysis results align with the dataset summary
2. Check if the research findings support or contradict the time series insights  
3. Assess whether the time series analysis goals were adequately addressed
4. Identify any discrepancies between expected vs actual time series EDA outcomes
5. Provide evidence-based conclusions and actionable recommendations for time series data
6. Highlight areas where external research validates time series analysis findings
7. Note any limitations or areas requiring further time series investigation
8. Evaluate the quality of temporal pattern detection and seasonal analysis
9. Assess the effectiveness of trend analysis and forecasting insights

Provide a comprehensive, evidence-based synthesis that validates the entire time series analysis pipeline."""
                next_step = "completed"
            
        elif current_step == "completed":
            # Workflow is already completed, no more actions needed
            return {
                "messages": state["messages"] + [AIMessage(content="Analysis workflow completed successfully!")],
                "current_step": "completed"
            }
        else:
            # Analysis complete or error occurred
            current_step = state.get("current_step", "")
            if current_step == "error":
                return {
                    "messages": state["messages"] + [AIMessage(content="Analysis workflow encountered errors and cannot continue.")],
                    "current_step": "completed"
                }
            else:
                return {
                    "messages": state["messages"] + [AIMessage(content="Analysis workflow completed successfully!")],
                    "current_step": "completed"
                }
        
        print(f"📊 Executing step: {next_step}")
        print(f"🎯 Prompt: {prompt[:200]}...")
        
        # Add the step-specific message
        messages = state["messages"] + [HumanMessage(content=prompt)]
        
        # Validate messages to avoid empty content issues with Gemini
        validated_messages = []
        for msg in messages:
            if hasattr(msg, 'content') and msg.content and str(msg.content).strip():
                validated_messages.append(msg)
            elif not hasattr(msg, 'content'):
                # Handle tool messages or other message types
                validated_messages.append(msg)
        
        print(f"🔍 Validated messages count: {len(validated_messages)}")
        
        # Call the model with tools
        try:
            response = model.bind_tools(self.tools).invoke(validated_messages)
        except Exception as e:
            print(f"❌ Model invocation error: {str(e)}")
            # Return error state
            return {
                "messages": validated_messages + [AIMessage(content=f"Error in model invocation: {str(e)}")],
                "current_step": "error",
                "errors": state.get("errors", []) + [str(e)]
            }
        
        # Update state with response
        updated_state = {
            "messages": validated_messages + [response],
            "current_step": next_step
        }
        
        # Debug: print response details
        print(f"🔍 Response type: {type(response)}")
        if hasattr(response, 'content'):
            print(f"🔍 Response content: {str(response.content)[:200]}...")
        if hasattr(response, 'tool_calls'):
            print(f"🔍 Tool calls: {len(response.tool_calls) if response.tool_calls else 0}")
        
        return updated_state
    
    def _process_tool_results(self, state: DataAnalysisState):
        """Process tool execution results and update state accordingly"""
        current_step = state.get("current_step", "start")
        messages = state.get("messages", [])
        
        print(f"🔧 Processing tool results for step: {current_step}")
        
        # Get the last tool message (should contain tool results)
        if messages and hasattr(messages[-1], 'content'):
            tool_result = messages[-1].content
            print(f"🔍 Tool result: {str(tool_result)[:200]}...")
            
            # Update state based on current step
            updated_state = {
                "messages": messages,
                "current_step": current_step
            }
            
            if current_step == "summarizing":
                updated_state["summary"] = str(tool_result)
                print("✅ Summary updated")
            elif current_step == "performing_eda":
                updated_state["eda_analysis"] = str(tool_result)
                print("✅ EDA analysis updated")
            elif current_step == "generating_search_prompts":
                updated_state["search_prompts"] = str(tool_result)
                print("✅ Search prompts updated")
            elif current_step == "researching":
                # Check if there was an error in research
                result_str = str(tool_result)
                if "Error:" in result_str or "failed:" in result_str:
                    print("⚠️ Research failed, but continuing with analysis...")
                    updated_state["research_results"] = f"Research failed due to technical issues: {result_str}"
                else:
                    updated_state["research_results"] = result_str
                print("✅ Research results updated")
            elif current_step == "verifying" or current_step == "completed":
                updated_state["verification"] = str(tool_result)
                updated_state["current_step"] = "completed"  # Mark as completed after verification
                print("✅ Verification updated - Analysis workflow completed!")
            
            return updated_state
        
        return state
    
    def _extract_first_search_prompt(self, prompts_text: str) -> str:
        """Extract the first search prompt from the generated prompts text"""
        lines = prompts_text.split('\n')
        
        # Look for numbered prompts (1. "prompt")
        for line in lines:
            line = line.strip()
            if line.startswith('1.'):
                # Extract content between quotes
                if '"' in line:
                    parts = line.split('"')
                    if len(parts) >= 2:
                        return parts[1].strip()
                # Fallback: extract after "1. "
                return line.replace('1.', '').strip().strip('"')
        
        # Look for any line that looks like a search query
        for line in lines:
            line = line.strip()
            if line and (
                'practices' in line.lower() or 
                'analysis' in line.lower() or 
                'trends' in line.lower() or
                'impact' in line.lower()
            ):
                # Clean up the line
                cleaned = line.strip('-').strip('•').strip()
                if '"' in cleaned:
                    parts = cleaned.split('"')
                    if len(parts) >= 2:
                        return parts[1].strip()
                return cleaned.strip()
        
        # Fallback: return a default search query
        return "Latest trends and insights in data analysis and business intelligence"
    
    async def run_analysis(self, dataset_path: str) -> Dict[str, Any]:
        """Run the complete analysis workflow"""
        if not self.graph:
            raise ValueError("Workflow not initialized. Call initialize() first.")
            
        print(f"🎯 Starting complete analysis workflow for: {dataset_path}")
        
        # Validate dataset path
        if not Path(dataset_path).exists():
            raise FileNotFoundError(f"Dataset file not found: {dataset_path}")
        
        # Initialize state
        initial_state = DataAnalysisState(
            messages=[],
            dataset_path=dataset_path,
            summary="",
            eda_analysis="", 
            search_prompts="",
            research_results="",
            verification="",
            current_step="start",
            errors=[]
        )
        
        try:
            # Run the workflow
            final_state = await self.graph.ainvoke(initial_state)
            
            print("✅ Analysis workflow completed successfully!")
            
            # Return comprehensive results
            return {
                "success": True,
                "dataset_path": dataset_path,
                "summary": final_state.get("summary", ""),
                "eda_analysis": final_state.get("eda_analysis", ""),
                "search_prompts": final_state.get("search_prompts", ""),
                "research_results": final_state.get("research_results", ""),
                "verification": final_state.get("verification", ""),
                "current_step": final_state.get("current_step", ""),
                "messages": final_state.get("messages", []),
                "errors": final_state.get("errors", [])
            }
            
        except Exception as e:
            print(f"❌ Analysis workflow failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "dataset_path": dataset_path
            }
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.client:
            # Note: MultiServerMCPClient might need specific cleanup
            # Check the documentation for proper cleanup methods
            pass

async def main():
    """Main function to demonstrate the workflow"""
    # Configuration - use environment variables for paths
    EDA_SERVER_PATH = os.getenv("EDA_MCP_SERVER_PATH", "./lida-mcp")
    DATASET_PATH = os.getenv("DATASET_PATH", os.path.abspath("./lida-mcp/sample_data_general_20250723_181651.csv"))
    
    print(f"🔧 EDA Server Path: {EDA_SERVER_PATH}")
    print(f"📁 Dataset Path: {DATASET_PATH}")
    
    # Verify dataset exists
    if not os.path.exists(DATASET_PATH):
        print(f"❌ Dataset file not found: {DATASET_PATH}")
        print("Available files in lida-mcp directory:")
        try:
            for file in os.listdir("./lida-mcp"):
                if file.endswith('.csv'):
                    print(f"  - {file}")
        except:
            pass
        return
    
    # Initialize workflow
    workflow = EdaWorkflow(EDA_SERVER_PATH)
    
    try:
        # Initialize the workflow
        await workflow.initialize()
        
        # Run the complete analysis
        results = await workflow.run_analysis(DATASET_PATH)
        
        # Display results
        print("\n" + "="*80)
        print("COMPLETE ANALYSIS RESULTS")
        print("="*80)
        
        if results["success"]:
            print(f"\n📁 Dataset: {results['dataset_path']}")
            
            # Display results with proper handling of empty strings
            summary = results.get('summary', '')
            if summary.strip():
                print(f"\n📊 Summary:\n{summary[:1000]}{'...' if len(summary) > 1000 else ''}")
            else:
                print(f"\n📊 Summary: No summary generated")
                
            eda_analysis = results.get('eda_analysis', '')
            if eda_analysis.strip():
                print(f"\n📊 Time Series EDA Analysis:\n{eda_analysis[:1000]}{'...' if len(eda_analysis) > 1000 else ''}")
            else:
                print(f"\n📊 Time Series EDA Analysis: No EDA analysis performed")
                
            search_prompts = results.get('search_prompts', '')
            if search_prompts.strip():
                print(f"\n🔍 Search Prompts:\n{search_prompts[:1000]}{'...' if len(search_prompts) > 1000 else ''}")
            else:
                print(f"\n🔍 Search Prompts: No search prompts generated")
                
            research_results = results.get('research_results', '')
            if research_results.strip():
                print(f"\n🌐 Research Results:\n{research_results[:1000]}{'...' if len(research_results) > 1000 else ''}")
            else:
                print(f"\n🌐 Research Results: No research conducted")
                
            verification = results.get('verification', '')
            if verification.strip():
                print(f"\n✅ Comprehensive Verification & Synthesis:\n{verification[:1000]}{'...' if len(verification) > 1000 else ''}")
            else:
                print(f"\n✅ Verification: No verification performed")
            
            if results.get('errors'):
                print(f"\n⚠️ Errors encountered: {results['errors']}")
                
            # Debug information
            print(f"\n🔍 Debug Info:")
            print(f"Current step: {results.get('current_step', 'unknown')}")
            print(f"Messages count: {len(results.get('messages', []))}")
        else:
            print(f"\n❌ Analysis failed: {results['error']}")
            
    except Exception as e:
        import traceback
        print(f"❌ Workflow execution failed: {str(e)}")
        print(f"🔍 Full traceback:")
        print(traceback.format_exc())
        
    finally:
        # Cleanup
        await workflow.cleanup()
        print("\n🧹 Cleanup completed")

if __name__ == "__main__":
    print("🚀 LangGraph EDA MCP Workflow")
    print("=" * 50)
    asyncio.run(main())
