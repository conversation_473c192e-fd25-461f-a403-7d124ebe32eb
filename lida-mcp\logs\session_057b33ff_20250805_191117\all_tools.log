[2025-08-05 19:12:43.940] [057b33ff] [0.000s] INFO in eda_mcp_tool_summarize_dataset: === TOOL START: summarize_dataset ===
[2025-08-05 19:12:43.941] [057b33ff] [0.001s] INFO in eda_mcp_tool_summarize_dataset: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "summary_method": "default",
  "temperature": 0.7,
  "use_cache": true,
  "output_directory": null
}
[2025-08-05 19:12:43.941] [057b33ff] [0.002s] DEBUG in eda_mcp_tool_summarize_dataset - log_tool_step(): STEP: Starting dataset summarization for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:12:43.943] [057b33ff] [0.003s] INFO in eda_mcp_tool_summarize_dataset: File operation: input_file_validated - C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:12:43.944] [057b33ff] [0.004s] DEBUG in eda_mcp_tool_summarize_dataset - log_tool_step(): STEP: Text generation config: temp=0.7, method=default
[2025-08-05 19:12:43.945] [057b33ff] [0.000s] DEBUG in eda_mcp_tool_create_fresh_lida_manager - log_tool_step(): STEP: Creating new LIDA Manager instance
[2025-08-05 19:12:43.945] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_create_fresh_lida_manager - log_tool_step(): STEP: LIDA Manager created successfully
[2025-08-05 19:12:43.946] [057b33ff] [0.006s] INFO in eda_mcp_tool_summarize_dataset: LIDA Operation: summarize
[2025-08-05 19:12:43.946] [057b33ff] [0.006s] INFO in eda_mcp_tool_summarize_dataset: LIDA Details: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "method": "default",
  "temperature": 0.7
}
[2025-08-05 19:12:43.946] [057b33ff] [0.006s] INFO in eda_mcp_tool_summarize_dataset: LLM Call: model=gpt-4o
[2025-08-05 19:12:43.946] [057b33ff] [0.006s] INFO in eda_mcp_tool_summarize_dataset: Tokens used: 2000
[2025-08-05 19:12:44.074] [057b33ff] [0.134s] INFO in eda_mcp_tool_summarize_dataset: LIDA Operation: summarize_complete
[2025-08-05 19:12:44.074] [057b33ff] [0.134s] INFO in eda_mcp_tool_summarize_dataset: LIDA Details: {
  "fields_count": 19,
  "insights_count": 0,
  "keywords_count": 0
}
[2025-08-05 19:12:44.074] [057b33ff] [0.134s] DEBUG in eda_mcp_tool_summarize_dataset - log_tool_step(): STEP: Summary completed successfully - 453 characters
[2025-08-05 19:12:44.075] [057b33ff] [0.134s] DEBUG in eda_mcp_tool_summarize_dataset - log_tool_step(): STEP: Execution completed in 0.133s
[2025-08-05 19:12:44.075] [057b33ff] [0.135s] INFO in eda_mcp_tool_summarize_dataset: === TOOL SUCCESS: summarize_dataset ===
[2025-08-05 19:12:44.075] [057b33ff] [0.135s] INFO in eda_mcp_tool_summarize_dataset: Result length: 453 characters
[2025-08-05 19:12:47.948] [057b33ff] [0.000s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:12:47.949] [057b33ff] [0.000s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "advanced",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": null,
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 19:12:47.949] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:12:47.949] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Analysis type: advanced, depth: comprehensive
[2025-08-05 19:12:47.992] [057b33ff] [0.000s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: basic
[2025-08-05 19:12:47.992] [057b33ff] [0.000s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: data_quality
[2025-08-05 19:12:47.992] [057b33ff] [0.000s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: advanced
[2025-08-05 19:12:47.992] [057b33ff] [0.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA engine initialized with modules
[2025-08-05 19:12:47.992] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: advanced
[2025-08-05 19:12:47.992] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:12:47.997] [057b33ff] [0.005s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:12:47.997] [057b33ff] [0.005s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:12:47.997] [057b33ff] [0.005s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['basic', 'data_quality', 'advanced']
[2025-08-05 19:12:47.997] [057b33ff] [0.005s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: basic
[2025-08-05 19:12:48.046] [057b33ff] [0.054s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: basic
[2025-08-05 19:12:48.046] [057b33ff] [0.054s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: data_quality
[2025-08-05 19:12:48.083] [057b33ff] [0.091s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: data_quality
[2025-08-05 19:12:48.083] [057b33ff] [0.091s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: advanced
