[2025-08-05 19:12:43.940] [057b33ff] [0.000s] INFO in eda_mcp_tool_summarize_dataset: === TOOL START: summarize_dataset ===
[2025-08-05 19:12:43.941] [057b33ff] [0.001s] INFO in eda_mcp_tool_summarize_dataset: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "summary_method": "default",
  "temperature": 0.7,
  "use_cache": true,
  "output_directory": null
}
[2025-08-05 19:12:43.941] [057b33ff] [0.002s] DEBUG in eda_mcp_tool_summarize_dataset - log_tool_step(): STEP: Starting dataset summarization for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:12:43.943] [057b33ff] [0.003s] INFO in eda_mcp_tool_summarize_dataset: File operation: input_file_validated - C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:12:43.944] [057b33ff] [0.004s] DEBUG in eda_mcp_tool_summarize_dataset - log_tool_step(): STEP: Text generation config: temp=0.7, method=default
[2025-08-05 19:12:43.945] [057b33ff] [0.000s] DEBUG in eda_mcp_tool_create_fresh_lida_manager - log_tool_step(): STEP: Creating new LIDA Manager instance
[2025-08-05 19:12:43.945] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_create_fresh_lida_manager - log_tool_step(): STEP: LIDA Manager created successfully
[2025-08-05 19:12:43.946] [057b33ff] [0.006s] INFO in eda_mcp_tool_summarize_dataset: LIDA Operation: summarize
[2025-08-05 19:12:43.946] [057b33ff] [0.006s] INFO in eda_mcp_tool_summarize_dataset: LIDA Details: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "method": "default",
  "temperature": 0.7
}
[2025-08-05 19:12:43.946] [057b33ff] [0.006s] INFO in eda_mcp_tool_summarize_dataset: LLM Call: model=gpt-4o
[2025-08-05 19:12:43.946] [057b33ff] [0.006s] INFO in eda_mcp_tool_summarize_dataset: Tokens used: 2000
[2025-08-05 19:12:44.074] [057b33ff] [0.134s] INFO in eda_mcp_tool_summarize_dataset: LIDA Operation: summarize_complete
[2025-08-05 19:12:44.074] [057b33ff] [0.134s] INFO in eda_mcp_tool_summarize_dataset: LIDA Details: {
  "fields_count": 19,
  "insights_count": 0,
  "keywords_count": 0
}
[2025-08-05 19:12:44.074] [057b33ff] [0.134s] DEBUG in eda_mcp_tool_summarize_dataset - log_tool_step(): STEP: Summary completed successfully - 453 characters
[2025-08-05 19:12:44.075] [057b33ff] [0.134s] DEBUG in eda_mcp_tool_summarize_dataset - log_tool_step(): STEP: Execution completed in 0.133s
[2025-08-05 19:12:44.075] [057b33ff] [0.135s] INFO in eda_mcp_tool_summarize_dataset: === TOOL SUCCESS: summarize_dataset ===
[2025-08-05 19:12:44.075] [057b33ff] [0.135s] INFO in eda_mcp_tool_summarize_dataset: Result length: 453 characters
[2025-08-05 19:12:47.948] [057b33ff] [0.000s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:12:47.949] [057b33ff] [0.000s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "advanced",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": null,
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 19:12:47.949] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:12:47.949] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Analysis type: advanced, depth: comprehensive
[2025-08-05 19:12:47.992] [057b33ff] [0.000s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: basic
[2025-08-05 19:12:47.992] [057b33ff] [0.000s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: data_quality
[2025-08-05 19:12:47.992] [057b33ff] [0.000s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: advanced
[2025-08-05 19:12:47.992] [057b33ff] [0.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA engine initialized with modules
[2025-08-05 19:12:47.992] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: advanced
[2025-08-05 19:12:47.992] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:12:47.997] [057b33ff] [0.005s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:12:47.997] [057b33ff] [0.005s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:12:47.997] [057b33ff] [0.005s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['basic', 'data_quality', 'advanced']
[2025-08-05 19:12:47.997] [057b33ff] [0.005s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: basic
[2025-08-05 19:12:48.046] [057b33ff] [0.054s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: basic
[2025-08-05 19:12:48.046] [057b33ff] [0.054s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: data_quality
[2025-08-05 19:12:48.083] [057b33ff] [0.091s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: data_quality
[2025-08-05 19:12:48.083] [057b33ff] [0.091s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: advanced
[2025-08-05 19:17:08.215] [057b33ff] [260.227s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Analysis 'advanced' failed: 'NoneType' object is not iterable
[2025-08-05 19:17:08.253] [057b33ff] [260.261s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: EDA analysis completed in 260.26 seconds
[2025-08-05 19:17:08.254] [057b33ff] [260.305s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA analysis completed in 260.26 seconds
[2025-08-05 19:17:08.257] [057b33ff] [260.308s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 260.308s
[2025-08-05 19:17:08.257] [057b33ff] [260.309s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:17:08.258] [057b33ff] [260.309s] INFO in eda_mcp_tool_perform_eda: Result length: 1758 characters
[2025-08-05 19:17:08.326] [057b33ff] [260.378s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:17:08.327] [057b33ff] [260.379s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "basic",
  "analysis_depth": "standard",
  "target_columns": null,
  "analysis_goals": null,
  "include_visualizations": false,
  "max_execution_time": 300,
  "sample_size": null
}
[2025-08-05 19:17:08.327] [057b33ff] [260.379s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:17:08.327] [057b33ff] [260.379s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Analysis type: basic, depth: standard
[2025-08-05 19:17:08.327] [057b33ff] [260.336s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: basic
[2025-08-05 19:17:08.327] [057b33ff] [260.336s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: data_quality
[2025-08-05 19:17:08.327] [057b33ff] [260.379s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA engine initialized with modules
[2025-08-05 19:17:08.327] [057b33ff] [260.336s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: basic
[2025-08-05 19:17:08.328] [057b33ff] [260.336s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:17:08.342] [057b33ff] [260.350s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:17:08.342] [057b33ff] [260.350s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:17:08.342] [057b33ff] [260.351s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['basic', 'data_quality']
[2025-08-05 19:17:08.342] [057b33ff] [260.351s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: basic
[2025-08-05 19:17:08.399] [057b33ff] [260.407s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: basic
[2025-08-05 19:17:08.399] [057b33ff] [260.407s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: data_quality
[2025-08-05 19:17:08.431] [057b33ff] [260.440s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: data_quality
[2025-08-05 19:17:08.434] [057b33ff] [260.442s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: EDA analysis completed in 0.11 seconds
[2025-08-05 19:17:08.434] [057b33ff] [260.486s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA analysis completed in 0.11 seconds
[2025-08-05 19:17:08.434] [057b33ff] [260.486s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 0.107s
[2025-08-05 19:17:08.434] [057b33ff] [260.486s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:17:08.434] [057b33ff] [260.486s] INFO in eda_mcp_tool_perform_eda: Result length: 1685 characters
[2025-08-05 19:17:12.894] [057b33ff] [264.946s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:17:12.895] [057b33ff] [264.946s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Analyze forecast accuracy and performance patterns across different profit centers and carlines"
  ],
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 19:17:12.895] [057b33ff] [264.947s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:17:12.895] [057b33ff] [264.947s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Analysis type: custom, depth: comprehensive
[2025-08-05 19:17:12.895] [057b33ff] [264.904s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 19:17:12.896] [057b33ff] [264.947s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA engine initialized with modules
[2025-08-05 19:17:12.896] [057b33ff] [264.904s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 19:17:12.896] [057b33ff] [264.904s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:17:12.902] [057b33ff] [264.910s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:17:12.902] [057b33ff] [264.910s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:17:12.902] [057b33ff] [264.910s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 19:17:12.902] [057b33ff] [264.911s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: custom
[2025-08-05 19:17:12.909] [057b33ff] [0.000s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Starting custom analysis with goal: 'Analyze forecast accuracy and performance patterns across different profit centers and carlines'
[2025-08-05 19:17:12.978] [057b33ff] [0.070s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Generating EDA code using LLM
[2025-08-05 19:17:26.679] [057b33ff] [13.771s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Generated 2717 characters of EDA code
[2025-08-05 19:17:26.680] [057b33ff] [13.772s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Executing generated EDA code
[2025-08-05 19:17:26.734] [057b33ff] [13.825s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Code executed successfully in 0.05 seconds
[2025-08-05 19:17:26.734] [057b33ff] [13.825s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Custom analysis completed successfully: True
[2025-08-05 19:17:26.734] [057b33ff] [278.742s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: custom
[2025-08-05 19:17:26.738] [057b33ff] [278.747s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: EDA analysis completed in 13.84 seconds
[2025-08-05 19:17:26.739] [057b33ff] [278.791s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA analysis completed in 13.84 seconds
[2025-08-05 19:17:26.739] [057b33ff] [278.791s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 13.844s
[2025-08-05 19:17:26.739] [057b33ff] [278.791s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:17:26.739] [057b33ff] [278.791s] INFO in eda_mcp_tool_perform_eda: Result length: 4059 characters
[2025-08-05 19:18:43.958] [057b33ff] [356.010s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:18:43.959] [057b33ff] [356.011s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Perform comprehensive time series analysis including trend analysis, seasonality detection, forecasting accuracy over time, and temporal patterns in order quantities and forecasts"
  ],
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 19:18:43.959] [057b33ff] [356.011s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:18:43.959] [057b33ff] [356.011s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Analysis type: custom, depth: comprehensive
[2025-08-05 19:18:43.960] [057b33ff] [355.968s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 19:18:43.960] [057b33ff] [356.012s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA engine initialized with modules
[2025-08-05 19:18:43.960] [057b33ff] [355.969s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 19:18:43.961] [057b33ff] [355.969s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:18:43.974] [057b33ff] [355.983s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:18:43.975] [057b33ff] [355.983s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:18:43.975] [057b33ff] [355.983s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 19:18:43.975] [057b33ff] [355.983s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: custom
[2025-08-05 19:18:43.975] [057b33ff] [91.066s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Starting custom analysis with goal: 'Perform comprehensive time series analysis including trend analysis, seasonality detection, forecasting accuracy over time, and temporal patterns in order quantities and forecasts'
[2025-08-05 19:18:44.018] [057b33ff] [91.109s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Generating EDA code using LLM
[2025-08-05 19:18:51.709] [057b33ff] [98.801s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Generated 2933 characters of EDA code
[2025-08-05 19:18:51.710] [057b33ff] [98.801s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Executing generated EDA code
[2025-08-05 19:18:51.816] [057b33ff] [98.907s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Code executed successfully in 0.10 seconds
[2025-08-05 19:18:51.816] [057b33ff] [98.908s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Custom analysis completed successfully: True
[2025-08-05 19:18:51.817] [057b33ff] [363.825s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: custom
[2025-08-05 19:18:51.821] [057b33ff] [363.829s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: EDA analysis completed in 7.86 seconds
[2025-08-05 19:18:51.821] [057b33ff] [363.873s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA analysis completed in 7.86 seconds
[2025-08-05 19:18:51.823] [057b33ff] [363.874s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 7.863s
[2025-08-05 19:18:51.823] [057b33ff] [363.875s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:18:51.823] [057b33ff] [363.875s] INFO in eda_mcp_tool_perform_eda: Result length: 4313 characters
[2025-08-05 19:18:58.120] [057b33ff] [370.171s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:18:58.120] [057b33ff] [370.172s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Analyze reference_date time series patterns, calculate forecast accuracy trends over time, detect seasonality in monthly data, and identify temporal correlations between different forecast types and actual orders"
  ],
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 19:18:58.120] [057b33ff] [370.172s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:18:58.120] [057b33ff] [370.172s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Analysis type: custom, depth: comprehensive
[2025-08-05 19:18:58.120] [057b33ff] [370.128s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 19:18:58.120] [057b33ff] [370.172s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA engine initialized with modules
[2025-08-05 19:18:58.120] [057b33ff] [370.129s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 19:18:58.120] [057b33ff] [370.129s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:18:58.125] [057b33ff] [370.134s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:18:58.126] [057b33ff] [370.134s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:18:58.126] [057b33ff] [370.134s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 19:18:58.126] [057b33ff] [370.134s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: custom
[2025-08-05 19:18:58.126] [057b33ff] [105.217s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Starting custom analysis with goal: 'Analyze reference_date time series patterns, calculate forecast accuracy trends over time, detect seasonality in monthly data, and identify temporal correlations between different forecast types and actual orders'
[2025-08-05 19:18:58.153] [057b33ff] [105.244s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Generating EDA code using LLM
[2025-08-05 19:19:03.440] [057b33ff] [110.531s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Generated 2563 characters of EDA code
[2025-08-05 19:19:03.440] [057b33ff] [110.532s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Executing generated EDA code
[2025-08-05 19:19:03.464] [057b33ff] [110.556s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Code executed successfully in 0.02 seconds
[2025-08-05 19:19:03.465] [057b33ff] [110.556s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Custom analysis completed successfully: True
[2025-08-05 19:19:03.465] [057b33ff] [375.474s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: custom
[2025-08-05 19:19:03.475] [057b33ff] [375.483s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: EDA analysis completed in 5.35 seconds
[2025-08-05 19:19:03.475] [057b33ff] [375.527s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA analysis completed in 5.35 seconds
[2025-08-05 19:19:03.478] [057b33ff] [375.530s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 5.358s
[2025-08-05 19:19:03.478] [057b33ff] [375.530s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:19:03.478] [057b33ff] [375.530s] INFO in eda_mcp_tool_perform_eda: Result length: 3743 characters
[2025-08-05 19:19:10.473] [057b33ff] [382.525s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:19:10.473] [057b33ff] [382.525s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Create detailed time series analysis with reference_date trends, calculate forecast bias and accuracy metrics over time periods, identify weekly/monthly patterns, and perform statistical tests for trend significance"
  ],
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 19:19:10.474] [057b33ff] [382.525s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:19:10.474] [057b33ff] [382.526s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Analysis type: custom, depth: comprehensive
[2025-08-05 19:19:10.474] [057b33ff] [382.482s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 19:19:10.474] [057b33ff] [382.526s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA engine initialized with modules
[2025-08-05 19:19:10.474] [057b33ff] [382.482s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 19:19:10.474] [057b33ff] [382.483s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:19:10.482] [057b33ff] [382.490s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:19:10.482] [057b33ff] [382.490s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:19:10.482] [057b33ff] [382.491s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 19:19:10.483] [057b33ff] [382.491s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing analysis: custom
[2025-08-05 19:19:10.483] [057b33ff] [117.574s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Starting custom analysis with goal: 'Create detailed time series analysis with reference_date trends, calculate forecast bias and accuracy metrics over time periods, identify weekly/monthly patterns, and perform statistical tests for trend significance'
[2025-08-05 19:19:10.558] [057b33ff] [117.650s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Generating EDA code using LLM
[2025-08-05 19:19:16.851] [057b33ff] [123.943s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Generated 2448 characters of EDA code
[2025-08-05 19:19:16.852] [057b33ff] [123.943s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Executing generated EDA code
[2025-08-05 19:19:17.008] [057b33ff] [124.100s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Code executed successfully in 0.14 seconds
[2025-08-05 19:19:17.009] [057b33ff] [124.100s] DEBUG in eda_mcp_tool_CustomAnalysisModule - log_tool_step(): STEP: Custom analysis completed successfully: True
[2025-08-05 19:19:17.009] [057b33ff] [389.017s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Completed analysis: custom
[2025-08-05 19:19:17.014] [057b33ff] [389.022s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: EDA analysis completed in 6.54 seconds
[2025-08-05 19:19:17.014] [057b33ff] [389.066s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA analysis completed in 6.54 seconds
[2025-08-05 19:19:17.019] [057b33ff] [389.070s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 6.545s
[2025-08-05 19:19:17.019] [057b33ff] [389.071s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:19:17.019] [057b33ff] [389.071s] INFO in eda_mcp_tool_perform_eda: Result length: 3775 characters
