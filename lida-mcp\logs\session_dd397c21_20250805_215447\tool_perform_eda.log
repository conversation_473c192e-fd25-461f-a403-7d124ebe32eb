[2025-08-05 21:54:53.084] [dd397c21] [0.027s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 21:54:53.085] [dd397c21] [0.028s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\Code\\AI\\Lida-MCP\\lida-mcp\\sample_data_general_20250723_181651.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Perform comprehensive time series analysis on the dataset. Identify temporal patterns, trends, seasonality, and time-based correlations. If date/time columns exist, analyze their distribution and patterns. Generate insights about changes over time, detect anomalies, and provide time-based forecasts or recommendations. Include autocorrelation analysis, trend decomposition, and identification of periodic behaviors."
  ],
  "include_visualizations": false,
  "max_execution_time": 300,
  "sample_size": null
}
[2025-08-05 21:54:53.088] [dd397c21] [0.031s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Starting EDA analysis for: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp\sample_data_general_20250723_181651.csv
[2025-08-05 21:54:53.088] [dd397c21] [0.031s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Analysis type: custom, depth: comprehensive
[2025-08-05 21:54:53.088] [dd397c21] [0.031s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎯 Analysis goals (1): ['Perform comprehensive time series analysis on the dataset. Identify temporal patterns, trends, seasonality, and time-based correlations. If date/time columns exist, analyze their distribution and patterns. Generate insights about changes over time, detect anomalies, and provide time-based forecasts or recommendations. Include autocorrelation analysis, trend decomposition, and identification of periodic behaviors.']
[2025-08-05 21:54:53.088] [dd397c21] [0.031s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📦 Importing EDA components
[2025-08-05 21:54:53.094] [dd397c21] [0.037s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ Input validation passed
[2025-08-05 21:54:53.094] [dd397c21] [0.037s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Creating EDA request object
[2025-08-05 21:54:53.095] [dd397c21] [0.037s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🏗️ Initializing EDA engine
[2025-08-05 21:54:53.095] [dd397c21] [0.038s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Registering CustomAnalysisModule with LLM integration
[2025-08-05 21:54:53.099] [dd397c21] [0.042s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔧 Custom module configured with model: gpt-4.1
[2025-08-05 21:54:53.099] [dd397c21] [0.042s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎛️ EDA engine initialized with all required modules
[2025-08-05 21:54:53.099] [dd397c21] [0.042s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🚀 Starting EDA analysis execution
