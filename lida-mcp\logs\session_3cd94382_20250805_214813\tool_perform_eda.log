[2025-08-05 21:48:19.040] [3cd94382] [0.056s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 21:48:19.041] [3cd94382] [0.057s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\Code\\AI\\Lida-MCP\\lida-mcp\\sample_data_general_20250723_181651.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Perform comprehensive time series analysis on the dataset. Identify temporal patterns, trends, seasonality, and time-based correlations. If date/time columns exist, analyze their distribution and patterns. Generate insights about changes over time, detect anomalies, and provide time-based forecasts or recommendations. Include autocorrelation analysis, trend decomposition, and identification of periodic behaviors."
  ],
  "include_visualizations": false,
  "max_execution_time": 300,
  "sample_size": null
}
[2025-08-05 21:48:19.043] [3cd94382] [0.059s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Starting EDA analysis for: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp\sample_data_general_20250723_181651.csv
[2025-08-05 21:48:19.043] [3cd94382] [0.059s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Analysis type: custom, depth: comprehensive
[2025-08-05 21:48:19.043] [3cd94382] [0.059s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎯 Analysis goals (1): ['Perform comprehensive time series analysis on the dataset. Identify temporal patterns, trends, seasonality, and time-based correlations. If date/time columns exist, analyze their distribution and patterns. Generate insights about changes over time, detect anomalies, and provide time-based forecasts or recommendations. Include autocorrelation analysis, trend decomposition, and identification of periodic behaviors.']
[2025-08-05 21:48:19.043] [3cd94382] [0.059s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📦 Importing EDA components
[2025-08-05 21:48:19.047] [3cd94382] [0.063s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ Input validation passed
[2025-08-05 21:48:19.047] [3cd94382] [0.063s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Creating EDA request object
[2025-08-05 21:48:19.047] [3cd94382] [0.063s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🏗️ Initializing EDA engine
[2025-08-05 21:48:19.047] [3cd94382] [0.063s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Registering CustomAnalysisModule with LLM integration
[2025-08-05 21:48:19.050] [3cd94382] [0.066s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔧 Custom module configured with model: gpt-4.1
[2025-08-05 21:48:19.051] [3cd94382] [0.066s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎛️ EDA engine initialized with all required modules
[2025-08-05 21:48:19.051] [3cd94382] [0.066s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🚀 Starting EDA analysis execution
