"""
EDA MCP Server

Provides access to EDA (Exploratory Data Analysis) API endpoints for automatic data visualization and analysis.
This server enables MCP clients to generate charts, analyze data, and create insights from datasets using natural language.

⚠️ IMPORTANT: This server requires Azure OpenAI or OpenAI API access which may incur costs.
Each tool that makes an LLM API call is marked with a cost warning. Please follow these guidelines:

1. Only use tools when explicitly requested by the user
2. Consider the size of your dataset as it affects processing time and costs
3. Chart generation and goal creation operations use LLM calls

Tools without cost warnings are free to use as they only process local data.
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
from io import StringIO
from typing import Literal, List, Dict, Any, Optional
from dotenv import load_dotenv
from pathlib import Path
import asyncio
import aiohttp
import ssl
import atexit

from mcp.server.fastmcp import FastMCP
from mcp.types import TextContent

from eda_mcp.model import (
    McpDataset, McpSummary, 
    EDARequest, EDAResult
)
from eda_mcp.utils import (
    make_error, make_output_path, make_output_file, handle_input_file,
    load_data_file, handle_large_text,
    create_sample_data, validate_textgen_config,
    LidaMcpError
)
from eda_mcp import __version__
from eda_mcp.logging_config import (
    get_logger, log_tool_execution, log_step, log_lida_op, log_llm_call,
    log_file_op, log_data_op, log_api_call, cleanup_logs
)

# Import LIDA components
try:
    from llmx import llm, TextGenerationConfig
    
    # First import pandas and create a patched version
    import pandas as pd
    
    # Store original read_excel function
    _original_read_excel = pd.read_excel
    
    def safe_read_excel(*args, **kwargs):
        """Safe wrapper for read_excel that removes encoding parameter"""
        # Remove encoding parameter if present (Excel files don't use encoding parameter)
        if 'encoding' in kwargs:
            del kwargs['encoding']
        return _original_read_excel(*args, **kwargs)
    
    # Monkey patch pandas
    pd.read_excel = safe_read_excel
    
    # Now import LIDA after patching pandas
    from lida import Manager
    
    import sys
    print("[OK] Successfully patched pandas.read_excel for Excel encoding compatibility", file=sys.stderr)
    
except ImportError as e:
    raise ImportError(
        f"Required packages not found: {e}. "
        f"Please install with: pip install lida llmx"
    )

load_dotenv()

# Initialize logging system
logger = get_logger()
logger.main_logger.info("Initializing EDA MCP Server")

# Register cleanup function for proper logging shutdown
atexit.register(cleanup_logs)

# Configuration
api_key = os.getenv("AZURE_OPENAI_API_KEY") or os.getenv("OPENAI_API_KEY")
endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
deployment_name = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
api_version = os.getenv("AZURE_OPENAI_API_VERSION", "2024-10-21")
base_path = os.getenv("EDA_MCP_BASE_PATH")

logger.main_logger.info(f"Configuration loaded - endpoint: {endpoint}, deployment: {deployment_name}")

# Jina DeepSearch Configuration
jina_api_key = os.getenv("JINA_API_KEY", "jina_704d29e58ed547cb9362e1bed70097b6WAIHKY_-OqfwbFhQqp43vfwwsAaO")
logger.main_logger.info(f"Jina API key configured: {jina_api_key[:20]}...")

if not api_key:
    logger.main_logger.error("No API key found in environment variables")
    raise ValueError(
        "API key environment variable is required. "
        "Set either AZURE_OPENAI_API_KEY or OPENAI_API_KEY"
    )

# Initialize LLM client
if endpoint and deployment_name:
    # Azure OpenAI configuration
    logger.main_logger.info("Configuring Azure OpenAI client")
    text_gen = llm(
        provider="openai",
        api_type="azure",
        azure_endpoint=endpoint,
        api_key=api_key,
        api_version=api_version,
    )
    default_model = deployment_name
    logger.main_logger.info(f"Azure OpenAI configured with model: {default_model}")
else:
    # OpenAI configuration
    logger.main_logger.info("Configuring OpenAI client")
    text_gen = llm(
        provider="openai",
        api_key=api_key,
    )
    default_model = "gpt-4o"
    logger.main_logger.info(f"OpenAI configured with model: {default_model}")

# Initialize global text generator (but create fresh LIDA managers per request)
# This prevents state issues between requests

def create_fresh_lida_manager():
    """Create a fresh LIDA Manager instance to avoid state issues"""
    log_step("create_fresh_lida_manager", "Creating new LIDA Manager instance")
    
    manager = Manager(text_gen=text_gen)
    log_step("create_fresh_lida_manager", "LIDA Manager created successfully")
    return manager










# Jina DeepSearch Configuration and Classes
from dataclasses import dataclass

@dataclass
class DeepSearchConfig:
    """Configuration for Jina DeepSearch"""
    base_url: str = "https://deepsearch.jina.ai/v1"
    model: str = "jina-deepsearch-v1"
    reasoning_effort: str = "low"  # low, medium, high
    stream: bool = False  # Disable streaming to avoid timeout issues
    timeout: int = 300  # Increased timeout to 5 minutes for DeepSearch processing

class JinaDeepSearch:
    """
    Jina DeepSearch client for MCP server integration
    """
    
    def __init__(self, api_key: Optional[str] = None, config: Optional[DeepSearchConfig] = None):
        """
        Initialize Jina DeepSearch client
        
        Args:
            api_key: Optional API key (not required for basic usage)
            config: Configuration settings
        """
        self.api_key = api_key
        self.config = config or DeepSearchConfig()
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        # Create SSL context for better compatibility
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False  # For development/testing
        ssl_context.verify_mode = ssl.CERT_NONE  # For development/testing
        
        # Create connector with SSL context
        connector = aiohttp.TCPConnector(ssl=ssl_context)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=aiohttp.ClientTimeout(total=self.config.timeout)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
        
    async def close(self):
        """Close the aiohttp session"""
        if self.session:
            await self.session.close()
            self.session = None
            
    async def search(
        self,
        query: str,
        reasoning_effort: Optional[str] = None,
        stream: Optional[bool] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Perform deep search query
        
        Args:
            query: Search query/question
            reasoning_effort: "low", "medium", or "high"
            stream: Whether to use streaming response
            max_tokens: Maximum tokens for response
            temperature: Sampling temperature
            
        Returns:
            Complete search result with answer, sources, and metadata
        """
        
        if not self.session:
            # Create SSL context for better compatibility
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False  # For development/testing
            ssl_context.verify_mode = ssl.CERT_NONE  # For development/testing
            
            # Create connector with SSL context
            connector = aiohttp.TCPConnector(ssl=ssl_context)
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            )
            
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "LIDA-MCP-Server/1.0"
        }
        
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
            
        payload = {
            "model": self.config.model,
            "messages": [
                {
                    "role": "user", 
                    "content": "Hi!"
                },
                {
                    "role": "assistant",
                    "content": "Hi, how can I help you?"
                },
                {
                    "role": "user", 
                    "content": query
                }
            ],
            "reasoning_effort": "low",  # Always use low for better performance
            "stream": stream if stream is not None else self.config.stream
        }
        
        # Add optional parameters
        if max_tokens:
            payload["max_tokens"] = max_tokens
        if temperature is not None:
            payload["temperature"] = temperature
            
        try:
            url = f"{self.config.base_url}/chat/completions"
            
            print(f"[DEBUG] DeepSearch URL: {url}", file=sys.stderr)
            print(f"[DEBUG] DeepSearch payload keys: {list(payload.keys())}", file=sys.stderr)
            print(f"[DEBUG] Reasoning effort: {payload['reasoning_effort']}", file=sys.stderr)
            print(f"[DEBUG] Stream: {payload['stream']}", file=sys.stderr)
            
            if payload["stream"]:
                try:
                    return await self._handle_streaming_response(url, headers, payload)
                except Exception as stream_error:
                    # If streaming fails, try non-streaming as fallback
                    print(f"[DEBUG] Streaming failed, trying non-streaming: {stream_error}", file=sys.stderr)
                    payload["stream"] = False
                    return await self._handle_single_response(url, headers, payload)
            else:
                return await self._handle_single_response(url, headers, payload)
                
        except aiohttp.ClientError as e:
            return {
                "error": f"HTTP Client Error: {str(e)}",
                "query": query,
                "success": False,
                "details": f"Connection or client error occurred"
            }
        except asyncio.TimeoutError as e:
            return {
                "error": f"Request timeout: {str(e)}",
                "query": query,
                "success": False,
                "details": f"Request took longer than {self.config.timeout} seconds"
            }
        except Exception as e:
            return {
                "error": f"DeepSearch request failed: {str(e)}",
                "query": query,
                "success": False,
                "details": f"Unexpected error: {type(e).__name__}"
            }
            
    async def _handle_streaming_response(self, url: str, headers: Dict, payload: Dict) -> Dict[str, Any]:
        """Handle streaming response from DeepSearch"""
        
        full_content = ""
        visited_urls = []
        token_usage = {}
        metadata = {}
        
        timeout = aiohttp.ClientTimeout(total=self.config.timeout)
        
        try:
            async with self.session.post(url, headers=headers, json=payload, timeout=timeout) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"HTTP {response.status}: {error_text}")
                
                # Check if response is actually streaming
                content_type = response.headers.get('content-type', '')
                if 'text/plain' in content_type or 'application/x-ndjson' in content_type or 'text/event-stream' in content_type:
                    # Handle streaming response
                    async for line in response.content:
                        if line:
                            line_str = line.decode('utf-8').strip()
                            if line_str.startswith('data: '):
                                data_str = line_str[6:]  # Remove 'data: ' prefix
                                
                                if data_str == '[DONE]':
                                    break
                                    
                                try:
                                    chunk_data = json.loads(data_str)
                                    
                                    # Extract content
                                    if 'choices' in chunk_data and chunk_data['choices']:
                                        delta = chunk_data['choices'][0].get('delta', {})
                                        if 'content' in delta:
                                            full_content += delta['content']
                                            
                                    # Extract metadata from final chunk
                                    if 'usage' in chunk_data:
                                        token_usage = chunk_data['usage']
                                    if 'visited_urls' in chunk_data:
                                        visited_urls = chunk_data['visited_urls']
                                    if 'metadata' in chunk_data:
                                        metadata = chunk_data['metadata']
                                        
                                except json.JSONDecodeError as e:
                                    # Skip malformed JSON chunks
                                    continue
                else:
                    # Handle as regular JSON response
                    result = await response.json()
                    if 'choices' in result and result['choices']:
                        full_content = result['choices'][0]['message']['content']
                    if 'usage' in result:
                        token_usage = result['usage']
                    if 'visited_urls' in result:
                        visited_urls = result['visited_urls']
                    if 'metadata' in result:
                        metadata = result['metadata']
                        
        except aiohttp.ClientError as e:
            raise Exception(f"HTTP Client Error in streaming: {str(e)}")
        except asyncio.TimeoutError as e:
            raise Exception(f"Timeout in streaming: {str(e)}")
        except json.JSONDecodeError as e:
            raise Exception(f"JSON decode error in streaming: {str(e)}")
        except Exception as e:
            raise Exception(f"Streaming response error: {str(e)}")
                                
        return {
            "answer": full_content,
            "visited_urls": visited_urls,
            "token_usage": token_usage,
            "metadata": metadata,
            "query": payload["messages"][0]["content"],
            "success": True
        }
        
    async def _handle_single_response(self, url: str, headers: Dict, payload: Dict) -> Dict[str, Any]:
        """Handle non-streaming response from DeepSearch"""
        
        payload["stream"] = False
        timeout = aiohttp.ClientTimeout(total=self.config.timeout)
        
        try:
            async with self.session.post(url, headers=headers, json=payload, timeout=timeout) as response:
                if response.status == 402:
                    error_text = await response.text()
                    raise Exception(f"Account balance insufficient: {error_text}")
                elif response.status == 401:
                    error_text = await response.text()
                    raise Exception(f"Authentication failed: {error_text}")
                elif response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"HTTP {response.status}: {error_text}")
                    
                # Get the raw response text first for debugging
                response_text = await response.text()
                
                try:
                    result = json.loads(response_text)
                except json.JSONDecodeError as json_err:
                    raise Exception(f"Invalid JSON response: {json_err}. Response text: {response_text[:200]}...")
                
                content = ""
                if 'choices' in result and result['choices']:
                    if 'message' in result['choices'][0] and 'content' in result['choices'][0]['message']:
                        content = result['choices'][0]['message']['content']
                    elif 'text' in result['choices'][0]:
                        content = result['choices'][0]['text']
                    else:
                        # Handle different response structures
                        choice = result['choices'][0]
                        content = str(choice.get('content', choice.get('text', str(choice))))
                elif 'answer' in result:
                    content = result['answer']
                elif 'content' in result:
                    content = result['content']
                else:
                    # Fallback - use the entire result as content
                    content = str(result)
                    
                return {
                    "answer": content,
                    "visited_urls": result.get('visited_urls', []),
                    "token_usage": result.get('usage', {}),
                    "metadata": result.get('metadata', {}),
                    "query": payload["messages"][0]["content"],
                    "success": True
                }
        except aiohttp.ClientError as e:
            raise Exception(f"HTTP Client Error in single response: {str(e)}")
        except asyncio.TimeoutError as e:
            raise Exception(f"Timeout in single response: {str(e)}")
        except Exception as e:
            # Preserve the original exception message
            raise Exception(f"Single response error: {str(e)}")

# Initialize FastMCP server
mcp = FastMCP("EDA")


def create_safe_textgen_config(temperature=0.7, max_tokens=4000, use_cache=True, model=None, task_type="general"):
    """Create a safe TextGenerationConfig with appropriate limits based on task type"""
    
    # Adjust token limits based on task type
    if task_type == "code_generation":
        # Code generation needs more tokens to avoid truncation
        default_tokens = 6000
        max_cap = 8000
    else:
        # General tasks (summaries, etc.)
        default_tokens = 2000
        max_cap = 4000
    
    actual_tokens = max_tokens if max_tokens > 0 else default_tokens
    
    return TextGenerationConfig(
        n=1,
        temperature=min(max(temperature, 0.1), 1.0),  # Clamp temperature between 0.1 and 1.0
        model=model or default_model,
        use_cache=use_cache,
        max_tokens=min(actual_tokens, max_cap)
    )





def normalize_column_names(df):
    """Normalize column names to handle different formats"""
    # Create a mapping of common variations
    column_mapping = {}
    
    for col in df.columns:
        normalized = col.strip()
        original_normalized = normalized
        
        # Handle year columns - convert spaces to underscores
        if normalized.startswith('CY '):
            new_name = normalized.replace(' ', '_')
            column_mapping[col] = new_name
        elif normalized.startswith('Q') and any(year in normalized for year in ['2024', '2025', '2026', '2027', '2028', '2029', '2030', '2031', '2032']):
            # Normalize quarterly columns
            new_name = normalized.replace(' ', '_')
            column_mapping[col] = new_name
        # Handle other columns with spaces/special characters
        elif ':' in normalized or ' ' in normalized:
            # More careful replacement to avoid triple underscores
            new_name = normalized
            
            # First handle colons - replace with double underscore
            if ':' in new_name:
                new_name = new_name.replace(':', '__')
            
            # Then handle spaces - replace with single underscore
            if ' ' in new_name:
                new_name = new_name.replace(' ', '_')
            
            # Clean up other characters
            new_name = (new_name
                       .replace('(', '')
                       .replace(')', '')
                       .replace('/', '_')
                       .replace('-', '_'))
            
            # Clean up multiple consecutive underscores
            while '___' in new_name:
                new_name = new_name.replace('___', '__')
            
            column_mapping[col] = new_name
    
    if column_mapping:
        df = df.rename(columns=column_mapping)
        print(f"[DEBUG] Normalized {len(column_mapping)} column names")
        for old, new in list(column_mapping.items())[:5]:  # Show first 5 mappings
            print(f"[DEBUG]   '{old}' -> '{new}'")
    
    return df


@mcp.tool(
    description="""Analyze and summarize a dataset file. Generates comprehensive insights about the data structure, content, and potential analysis opportunities.
    
    Supported file formats: CSV, JSON, Excel (.xlsx, .xls), TSV, Parquet, TXT
    
    Args:
        file_path: Path to the dataset file to analyze
        summary_method: Method to use for summarization (default, columns, sample)
        textgen_config: LLM configuration (temperature, model, etc.)
        output_directory: Directory to save analysis results (optional)
    
    Returns:
        Comprehensive dataset summary with field information, insights, and analysis suggestions.
    """
)
@log_tool_execution("summarize_dataset")
def summarize_dataset(
    file_path: str,
    summary_method: Literal["default", "columns", "sample"] = "default",
    temperature: float = 0.7,
    use_cache: bool = True,
    output_directory: str | None = None,
) -> TextContent:
    """Analyze and summarize a dataset file"""
    try:
        log_step("summarize_dataset", f"Starting dataset summarization for: {file_path}")
        data_path = handle_input_file(file_path, check_data_file=True)
        log_file_op("summarize_dataset", "input_file_validated", str(data_path), success=True)
        
        # Configure text generation with safe limits for summarization
        textgen_config = create_safe_textgen_config(
            temperature=temperature,
            max_tokens=2000,  # Conservative for summaries
            use_cache=use_cache,
            task_type="general"
        )
        log_step("summarize_dataset", f"Text generation config: temp={temperature}, method={summary_method}")
        
        # Create fresh LIDA manager to avoid state issues
        lida_manager = create_fresh_lida_manager()
        
        # Generate summary using LIDA
        log_lida_op("summarize_dataset", "summarize", {
            "file_path": str(data_path),
            "method": summary_method,
            "temperature": temperature
        })
        
        log_llm_call("summarize_dataset", default_model, tokens_used=textgen_config.max_tokens)
        
        summary = lida_manager.summarize(
            str(data_path),
            summary_method=summary_method,
            textgen_config=textgen_config
        )
        
        log_lida_op("summarize_dataset", "summarize_complete", {
            "fields_count": len(summary.get('field_names', [])),
            "insights_count": len(summary.get('insights', [])),
            "keywords_count": len(summary.get('keywords', []))
        })
        
        # Format summary for display
        summary_text = f"""Dataset Summary: {summary.get('dataset_description', 'N/A')}

File Information:
- Name: {summary.get('file_name', data_path.name)}
- Fields: {len(summary.get('field_names', []))} columns
- Columns: {', '.join(summary.get('field_names', []))}

Key Insights:
{chr(10).join(f"• {insight}" for insight in summary.get('insights', []))}

Keywords: {', '.join(summary.get('keywords', []))}

Dataset Description:
{summary.get('dataset_description', 'No description available')}"""
        
        # Save summary if output directory specified
        if output_directory:
            log_step("summarize_dataset", f"Saving summary to: {output_directory}")
            output_path = make_output_path(output_directory, base_path)
            summary_file = make_output_file(
                "summary", 
                data_path.stem, 
                output_path, 
                "json"
            )
            
            with open(output_path / summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, default=str)
            
            log_file_op("summarize_dataset", "summary_saved", str(output_path / summary_file), success=True)
            summary_text += f"\n\nSummary saved to: {output_path / summary_file}"
        
        log_step("summarize_dataset", f"Summary completed successfully - {len(summary_text)} characters")
        return TextContent(type="text", text=summary_text)
        
    except LidaMcpError as e:
        # Handle known LIDA MCP errors
        log_step("summarize_dataset", f"LIDA MCP Error: {str(e)}")
        return TextContent(type="text", text=f"Error: {str(e)}")
    except Exception as e:
        # Return proper error response
        error_message = f"Failed to summarize dataset: {str(e)}"
        log_step("summarize_dataset", f"Unexpected error: {error_message}")
        return TextContent(type="text", text=error_message)






@mcp.tool(
    description="""Perform complete automated data analysis workflow. Combines summarization into one comprehensive analysis.

    ⚠️ COST WARNING: This tool makes LLM API calls which may incur costs. Only use when explicitly requested by the user.
    
    Args:
        file_path: Path to the dataset file
        temperature: Creativity level for generation
        save_outputs: Whether to save all outputs to files
        output_directory: Directory to save all results
        
    Returns:
        Complete analysis results with summary and insights.
    """
)
@log_tool_execution("complete_analysis")
def complete_analysis(
    file_path: str,
    temperature: float = 0.7,
    save_outputs: bool = True,
    output_directory: str | None = None,
) -> TextContent:
    """Perform complete automated data analysis"""
    try:
        log_step("complete_analysis", f"Starting complete analysis for: {file_path}")
        log_step("complete_analysis", f"Parameters: temp={temperature}")
        
        data_path = handle_input_file(file_path, check_data_file=True)
        log_file_op("complete_analysis", "input_file_validated", str(data_path), success=True)
        
        # Configure text generation
        textgen_config = TextGenerationConfig(
            n=1,
            temperature=temperature,
            model=default_model,
            use_cache=True
        )
        log_step("complete_analysis", f"Text generation config created with temp={temperature}")
        
        # Create fresh LIDA manager to avoid state issues
        lida_manager = create_fresh_lida_manager()
        
        analysis_results = []
        analysis_results.append(f"COMPLETE DATA ANALYSIS REPORT")
        analysis_results.append("=" * 50)
        analysis_results.append(f"Dataset: {data_path.name}")
        analysis_results.append(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        analysis_results.append("")
        
        # Step 1: Summarize dataset
        log_step("complete_analysis", "Step 1: Dataset summarization")
        analysis_results.append("1. DATASET SUMMARY")
        analysis_results.append("-" * 20)
        
        log_lida_op("complete_analysis", "summarize", {"file_path": str(data_path)})
        summary = lida_manager.summarize(
            str(data_path),
            summary_method="default",
            textgen_config=textgen_config
        )
        
        log_lida_op("complete_analysis", "summary_complete", {
            "fields": len(summary.get('field_names', [])),
            "insights": len(summary.get('insights', [])),
            "keywords": len(summary.get('keywords', []))
        })
        
        analysis_results.append(f"Fields: {', '.join(summary.get('field_names', []))}")
        analysis_results.append(f"Description: {summary.get('dataset_description', 'N/A')}")
        analysis_results.append(f"Keywords: {', '.join(summary.get('keywords', []))}")
        analysis_results.append("")
        
        # Step 2: Summary insights
        log_step("complete_analysis", "Step 2: Compiling insights")
        analysis_results.append("2. KEY INSIGHTS")
        analysis_results.append("-" * 20)
        
        for insight in summary.get('insights', []):
            analysis_results.append(f"• {insight}")
        
        analysis_results.append("")
        analysis_results.append(f"Analysis completed successfully!")
        analysis_results.append(f"Summary generated with {len(summary.get('insights', []))} key insights.")
        
        # Save complete report if requested
        if save_outputs and output_directory:
            log_step("complete_analysis", f"Saving complete report to: {output_directory}")
            output_path = make_output_path(output_directory, base_path)
            report_file = make_output_file(
                "analysis_report",
                data_path.stem,
                output_path,
                "txt"
            )
            
            with open(output_path / report_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(analysis_results))
            
            log_file_op("complete_analysis", "report_saved", str(output_path / report_file), success=True)
            analysis_results.append(f"\nComplete report saved to: {output_path / report_file}")
        
        full_report = '\n'.join(analysis_results)
        
        log_step("complete_analysis", f"Complete analysis finished - {len(full_report)} characters")
        
        # Handle large content
        return TextContent(
            type="text", 
            text=handle_large_text(full_report, max_length=15000, content_type="analysis report")
        )
        
    except Exception as e:
        log_step("complete_analysis", f"Complete analysis failed: {str(e)}")
        make_error(f"Failed to complete analysis: {str(e)}")


@mcp.tool(
    description="""Create sample dataset for testing LIDA functionality. Generates realistic sample data in various domains.
    
    Args:
        data_type: Type of sample data to create
        rows: Number of rows to generate (10-1000)
        output_directory: Directory to save the sample file
        
    Returns:
        Path to created sample dataset file.
    """
)
@log_tool_execution("create_sample_dataset")
def create_sample_dataset(
    data_type: Literal["sales", "financial", "customer", "general"] = "sales",
    rows: int = 50,
    output_directory: str | None = None,
) -> TextContent:
    """Create a sample dataset for testing"""
    try:
        log_step("create_sample_dataset", f"Creating sample dataset: type={data_type}, rows={rows}")
        
        if not 10 <= rows <= 1000:
            log_step("create_sample_dataset", f"Invalid rows: {rows}, must be 10-1000")
            make_error("Number of rows must be between 10 and 1000")
        
        # Get base sample data
        log_step("create_sample_dataset", f"Generating base {data_type} data")
        sample_data = create_sample_data(data_type)
        
        # Load into DataFrame and extend if needed
        df = pd.read_csv(StringIO(sample_data))
        original_rows = len(df)
        log_data_op("create_sample_dataset", "base_data_loaded", {
            "data_type": data_type,
            "original_rows": original_rows,
            "columns": list(df.columns)
        })
        
        if rows > len(df):
            log_step("create_sample_dataset", f"Extending data from {len(df)} to {rows} rows")
            # Duplicate and modify data to reach desired row count
            multiplier = (rows // len(df)) + 1
            extended_df = pd.concat([df] * multiplier, ignore_index=True)
            
            # Add some variation to duplicated data
            if 'sales' in df.columns:
                extended_df['sales'] = extended_df['sales'] * (1 + (extended_df.index % 10 - 5) * 0.1)
            if 'profit' in df.columns:
                extended_df['profit'] = extended_df['profit'] * (1 + (extended_df.index % 8 - 4) * 0.05)
            if 'value' in df.columns:
                extended_df['value'] = extended_df['value'] * (1 + (extended_df.index % 6 - 3) * 0.08)
            
            df = extended_df.head(rows)
            log_step("create_sample_dataset", f"Data extended with variations applied")
            
        elif rows < len(df):
            df = df.head(rows)
            log_step("create_sample_dataset", f"Data truncated to {rows} rows")
        
        # Save the dataset
        output_path = make_output_path(output_directory or "", base_path)
        output_file = make_output_file(
            "sample_data",
            data_type,
            output_path,
            "csv"
        )
        
        full_path = output_path / output_file
        df.to_csv(full_path, index=False)
        
        log_file_op("create_sample_dataset", "dataset_saved", str(full_path), success=True)
        log_data_op("create_sample_dataset", "final_dataset", {
            "rows": len(df),
            "columns": len(df.columns),
            "file_size_bytes": full_path.stat().st_size
        })
        
        result_text = f"Sample dataset created successfully!\n\n"
        result_text += f"Type: {data_type}\n"
        result_text += f"Rows: {len(df)}\n"
        result_text += f"Columns: {', '.join(df.columns)}\n"
        result_text += f"File: {full_path}\n\n"
        result_text += f"You can now use this file with other LIDA tools for analysis and visualization."
        
        log_step("create_sample_dataset", f"Sample dataset creation completed - {len(result_text)} characters")
        
        return TextContent(type="text", text=result_text)
        
    except Exception as e:
        error_msg = f"Failed to create sample dataset: {str(e)}"
        log_step("create_sample_dataset", f"Creation failed: {error_msg}")
        make_error(error_msg)


@mcp.tool(
    description="""Get dataset information and preview without full analysis. Quickly examine data structure and content.
    
    Args:
        file_path: Path to the dataset file
        preview_rows: Number of rows to preview (1-20)
        show_stats: Whether to include basic statistics
        
    Returns:
        Dataset information with structure, preview, and basic statistics.
    """
)
@log_tool_execution("inspect_dataset")
def inspect_dataset(
    file_path: str,
    preview_rows: int = 5,
    show_stats: bool = True,
) -> TextContent:
    """Inspect dataset structure and content"""
    try:
        log_step("inspect_dataset", f"Starting dataset inspection for: {file_path}")
        
        if not 1 <= preview_rows <= 20:
            log_step("inspect_dataset", f"Invalid preview_rows: {preview_rows}")
            make_error("Preview rows must be between 1 and 20")
        
        data_path = handle_input_file(file_path, check_data_file=True)
        log_file_op("inspect_dataset", "input_file_validated", str(data_path), success=True)
        
        # Load the dataset
        log_data_op("inspect_dataset", "loading_data", {"file_path": str(data_path)})
        df = load_data_file(data_path)
        
        file_size_kb = data_path.stat().st_size / 1024
        log_data_op("inspect_dataset", "data_loaded", {
            "rows": df.shape[0],
            "columns": df.shape[1],
            "file_size_kb": round(file_size_kb, 1),
            "dtypes": df.dtypes.value_counts().to_dict()
        })
        
        result_text = f"DATASET INSPECTION: {data_path.name}\n"
        result_text += "=" * 50 + "\n\n"
        
        # Basic information
        result_text += f"Shape: {df.shape[0]} rows × {df.shape[1]} columns\n"
        result_text += f"File size: {file_size_kb:.1f} KB\n\n"
        
        # Column information
        log_step("inspect_dataset", "Analyzing column information")
        result_text += "COLUMNS:\n"
        result_text += "-" * 10 + "\n"
        for i, col in enumerate(df.columns, 1):
            dtype = str(df[col].dtype)
            null_count = df[col].isnull().sum()
            result_text += f"{i:2}. {col:<20} ({dtype:<10}) - {null_count} nulls\n"
        
        result_text += "\n"
        
        # Data preview
        log_step("inspect_dataset", f"Creating data preview with {preview_rows} rows")
        result_text += f"DATA PREVIEW (first {preview_rows} rows):\n"
        result_text += "-" * 30 + "\n"
        
        preview_df = df.head(preview_rows)
        result_text += preview_df.to_string(max_cols=10, max_colwidth=20)
        result_text += "\n\n"
        
        # Basic statistics
        if show_stats:
            log_step("inspect_dataset", "Computing basic statistics")
            result_text += "BASIC STATISTICS:\n"
            result_text += "-" * 20 + "\n"
            
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                stats_df = df[numeric_cols].describe()
                result_text += stats_df.round(2).to_string()
                result_text += "\n\n"
                log_step("inspect_dataset", f"Statistics computed for {len(numeric_cols)} numeric columns")
            
            # Data types summary
            result_text += "DATA TYPES SUMMARY:\n"
            type_counts = df.dtypes.value_counts()
            for dtype, count in type_counts.items():
                result_text += f"  {dtype}: {count} columns\n"
            
            result_text += "\n"
            
            # Missing values summary
            missing = df.isnull().sum()
            if missing.sum() > 0:
                result_text += "MISSING VALUES:\n"
                missing_cols = 0
                for col, miss_count in missing[missing > 0].items():
                    pct = (miss_count / len(df)) * 100
                    result_text += f"  {col}: {miss_count} ({pct:.1f}%)\n"
                    missing_cols += 1
                log_step("inspect_dataset", f"Found missing values in {missing_cols} columns")
            else:
                result_text += "No missing values found.\n"
                log_step("inspect_dataset", "No missing values detected")
        
        result_text += f"\nDataset is ready for analysis with LIDA tools!"
        
        log_step("inspect_dataset", f"Dataset inspection completed - {len(result_text)} characters")
        
        return TextContent(
            type="text",
            text=handle_large_text(result_text, max_length=8000, content_type="dataset inspection")
        )
        
    except Exception as e:
        error_msg = f"Failed to inspect dataset: {str(e)}"
        log_step("inspect_dataset", f"Inspection failed: {error_msg}")
        make_error(error_msg)


@mcp.tool(
    description="""Test EDA MCP server functionality with built-in test data. Verifies that all components are working correctly.
    
    Args:
        test_type: Type of test to run
        save_outputs: Whether to save test outputs
        output_directory: Directory for test outputs
        
    Returns:
        Test results and status information.
    """
)
@log_tool_execution("test_lida_functionality")
def test_lida_functionality(
    test_type: Literal["basic", "complete"] = "basic",
    save_outputs: bool = False,
    output_directory: str | None = None,
) -> TextContent:
    """Test EDA MCP server functionality"""
    try:
        log_step("test_lida_functionality", f"Starting functionality test: {test_type}")
        
        test_results = []
        test_results.append("EDA MCP SERVER FUNCTIONALITY TEST")
        test_results.append("=" * 40)
        test_results.append(f"Test Type: {test_type}")
        test_results.append(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        test_results.append("")
        
        # Create test data
        log_step("test_lida_functionality", "Creating test data")
        test_data = create_sample_data("sales")
        
        # Save to temporary file
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(test_data)
            temp_file = f.name
        
        log_file_op("test_lida_functionality", "temp_file_created", temp_file, success=True)
        
        try:
            # Test 1: Dataset loading and inspection
            log_step("test_lida_functionality", "Test 1: Dataset loading")
            test_results.append("Test 1: Dataset Loading")
            test_results.append("-" * 25)
            
            df = load_data_file(Path(temp_file))
            log_data_op("test_lida_functionality", "test_data_loaded", {
                "rows": df.shape[0],
                "columns": df.shape[1]
            })
            test_results.append(f"✓ Dataset loaded successfully ({df.shape[0]} rows, {df.shape[1]} cols)")
            test_results.append("")
            
            if test_type in ["complete", "basic"]:
                # Test 2: Summarization
                log_step("test_lida_functionality", "Test 2: Dataset summarization")
                test_results.append("Test 2: Dataset Summarization")
                test_results.append("-" * 30)
                
                # Create fresh LIDA manager to avoid state issues
                lida_manager = create_fresh_lida_manager()
                
                textgen_config = TextGenerationConfig(
                    n=1,
                    temperature=0.7,
                    model=default_model,
                    use_cache=True
                )
                
                log_lida_op("test_lida_functionality", "test_summarize", {"file_path": temp_file})
                log_llm_call("test_lida_functionality", default_model, tokens_used=textgen_config.max_tokens)
                
                summary = lida_manager.summarize(
                    temp_file,
                    summary_method="default",
                    textgen_config=textgen_config
                )
                
                log_lida_op("test_lida_functionality", "test_summary_complete", {
                    "fields": len(summary.get('field_names', [])),
                    "description_length": len(summary.get('dataset_description', ''))
                })
                
                test_results.append(f"✓ Summary generated successfully")
                test_results.append(f"  Fields: {len(summary.get('field_names', []))}")
                test_results.append(f"  Description: {summary.get('dataset_description', 'N/A')[:50]}...")
                test_results.append("")
            

            
            # Test completion
            log_step("test_lida_functionality", "Compiling test summary")
            test_results.append("TEST SUMMARY")
            test_results.append("-" * 15)
            test_results.append("✓ All core components functional")
            test_results.append("✓ LIDA integration working")
            test_results.append("✓ LLM connectivity established")
            test_results.append("")
            test_results.append("🎉 EDA MCP Server is ready for use!")
            
        finally:
            # Clean up temporary file
            log_file_op("test_lida_functionality", "temp_file_cleanup", temp_file, success=True)
            os.unlink(temp_file)
        
        log_step("test_lida_functionality", f"Functionality test completed successfully - {test_type}")
        
        return TextContent(
            type="text",
            text='\n'.join(test_results)
        )
        
    except Exception as e:
        error_msg = f"Functionality test failed: {str(e)}"
        log_step("test_lida_functionality", f"Test failed: {error_msg}")
        make_error(error_msg)


@mcp.tool(
    description="""Test Jina DeepSearch connectivity and configuration. Use this to verify that the Jina API is working correctly.
    
    Returns:
        Connection test results and configuration status.
    """
)
@log_tool_execution("test_jina_connectivity")
async def test_jina_connectivity() -> TextContent:
    """Test Jina DeepSearch connectivity"""
    try:
        log_step("test_jina_connectivity", "Starting Jina DeepSearch connectivity test")
        
        test_results = []
        test_results.append("JINA DEEPSEARCH CONNECTIVITY TEST")
        test_results.append("=" * 40)
        
        # Test 1: Configuration check
        log_step("test_jina_connectivity", "Testing configuration")
        test_results.append("1. Configuration Check:")
        test_results.append(f"   API Key: {jina_api_key[:20]}... (configured)")
        test_results.append(f"   Base URL: https://deepsearch.jina.ai/v1")
        test_results.append("")
        
        # Test 2: Simple connectivity test
        log_step("test_jina_connectivity", "Testing connectivity with simple query")
        test_results.append("2. Connectivity Test:")
        
        try:
            log_api_call("test_jina_connectivity", "Jina DeepSearch", "deepsearch.jina.ai")
            
            async with JinaDeepSearch(api_key=jina_api_key) as searcher:
                result = await searcher.search(
                    query="Hello, this is a test query",
                    reasoning_effort="low",
                    stream=False
                )
                
                if result["success"]:
                    response_length = len(result.get('answer', ''))
                    urls_count = len(result.get('visited_urls', []))
                    
                    log_api_call("test_jina_connectivity", "Jina DeepSearch", "deepsearch.jina.ai", 
                               success=True, response_size=response_length)
                    log_step("test_jina_connectivity", f"Test successful: {response_length} chars, {urls_count} URLs")
                    
                    test_results.append("   ✅ Connection successful")
                    test_results.append(f"   ✅ Response received: {response_length} characters")
                    if result.get('token_usage'):
                        usage = result['token_usage']
                        test_results.append(f"   ✅ Token usage: {usage}")
                        log_step("test_jina_connectivity", f"Token usage: {usage}")
                    if result.get('visited_urls'):
                        test_results.append(f"   ✅ Sources consulted: {len(result['visited_urls'])} URLs")
                else:
                    log_api_call("test_jina_connectivity", "Jina DeepSearch", "deepsearch.jina.ai", 
                               success=False, error=result.get('error', 'Unknown error'))
                    log_step("test_jina_connectivity", f"Connection failed: {result.get('error', 'Unknown error')}")
                    
                    test_results.append("   ❌ Connection failed")
                    test_results.append(f"   Error: {result.get('error', 'Unknown error')}")
                    if result.get('details'):
                        test_results.append(f"   Details: {result['details']}")
                        
        except Exception as e:
            log_api_call("test_jina_connectivity", "Jina DeepSearch", "deepsearch.jina.ai", 
                        success=False, error=str(e))
            log_step("test_jina_connectivity", f"Exception occurred: {str(e)}")
            
            test_results.append(f"   ❌ Exception occurred: {str(e)}")
            test_results.append(f"   Error type: {type(e).__name__}")
        
        test_results.append("")
        test_results.append("Test completed.")
        
        log_step("test_jina_connectivity", "Jina connectivity test completed")
        
        return TextContent(
            type="text",
            text='\n'.join(test_results)
        )
        
    except Exception as e:
        error_msg = f"Connectivity test failed: {str(e)}"
        log_step("test_jina_connectivity", f"Test failed: {error_msg}")
        make_error(error_msg)


@mcp.tool(
    description="""Generate search prompts based on dataset analysis. Creates intelligent search queries that can be used with Jina DeepSearch or other search tools.
    
    Args:
        dataset_info: Complete information about the dataset including analysis results, insights, and metadata
        num_prompts: Number of search prompts to generate (1-10)
        search_context: Context or goal for the search prompts to focus the generation
        
    Returns:
        List of intelligent search prompts tailored to the dataset content and search context.
    """
)
def generate_search_prompts(
    dataset_info: str,
    num_prompts: int = 5,
    search_context: str = "general data analysis and insights",
) -> TextContent:
    """Generate search prompts based on dataset information"""
    try:
        # Validate inputs
        if not 1 <= num_prompts <= 10:
            return TextContent(type="text", text="Error: Number of prompts must be between 1 and 10")
        
        if not dataset_info or not dataset_info.strip():
            return TextContent(type="text", text="Error: Dataset information is required")
        
        import sys
        print(f"[DEBUG] Generating {num_prompts} search prompts", file=sys.stderr)
        
        # Create a focused prompt for search query generation
        generation_prompt = f"""
Based on the dataset analysis and information below, generate {num_prompts} intelligent search queries that would help provide deeper insights and context about this data.

Dataset Information:
{dataset_info}

Search Context: {search_context}

Generate search queries that would help answer questions like:
- What are the industry standards for this type of data?
- What external factors might influence these metrics?
- What are the latest trends related to this domain?
- How do these results compare to market benchmarks?
- What methodologies are recommended for this analysis?

Requirements:
1. Each query should be specific and actionable
2. Queries should be suitable for web search or research databases
3. Include relevant domain terminology from the dataset
4. Make queries that would provide complementary insights to the dataset
5. Ensure queries are diverse and cover different aspects

Return exactly {num_prompts} search queries, each on a new line, numbered 1-{num_prompts}.
"""
        
        # Generate search prompts using LLM
        prompt_config = create_safe_textgen_config(
            temperature=0.7,
            max_tokens=3000,
            use_cache=False,
            task_type="general"
        )
        
        try:
            # Use the text generation correctly with proper message format
            from llmx import TextGenerationConfig
            
            # Create the message format that the LLM expects
            messages = [
                {
                    "role": "user",
                    "content": generation_prompt
                }
            ]
            
            prompt_response = text_gen.generate(
                messages=messages,
                config=prompt_config
            )
            
            # Extract the generated text
            if hasattr(prompt_response, 'text') and prompt_response.text:
                generated_text = prompt_response.text[0].content if hasattr(prompt_response.text[0], 'content') else str(prompt_response.text[0])
            elif hasattr(prompt_response, 'choices') and prompt_response.choices:
                generated_text = prompt_response.choices[0].message.content if hasattr(prompt_response.choices[0], 'message') else str(prompt_response.choices[0])
            else:
                generated_text = str(prompt_response)
            
            print(f"[DEBUG] Generated search prompts: {len(generated_text)} characters", file=sys.stderr)
            
            # Parse the generated prompts
            lines = generated_text.strip().split('\n')
            prompts = []
            
            for line in lines:
                line = line.strip()
                if line and (line[0].isdigit() or line.startswith('-') or line.startswith('•')):
                    # Clean up the line to extract just the prompt
                    clean_prompt = line
                    # Remove numbering
                    if '. ' in clean_prompt:
                        clean_prompt = clean_prompt.split('. ', 1)[1]
                    elif '- ' in clean_prompt:
                        clean_prompt = clean_prompt.split('- ', 1)[1]
                    elif '• ' in clean_prompt:
                        clean_prompt = clean_prompt.split('• ', 1)[1]
                    
                    clean_prompt = clean_prompt.strip()
                    if clean_prompt and len(clean_prompt) > 10:
                        prompts.append(clean_prompt)
            
            # Ensure we have the requested number of prompts
            prompts = prompts[:num_prompts]
            
            if len(prompts) == 0:
                return TextContent(type="text", text="Error: Failed to generate search prompts. Please try with different dataset information.")
            
        except Exception as llm_error:
            print(f"[DEBUG] LLM generation failed: {str(llm_error)}", file=sys.stderr)
            return TextContent(type="text", text=f"Error: Failed to generate search prompts using LLM: {str(llm_error)}")
        
        # Format the response
        result_text = f"SEARCH PROMPTS GENERATED FROM DATASET ANALYSIS\n"
        result_text += "=" * 55 + "\n\n"
        result_text += f"Context: {search_context}\n"
        result_text += f"Generated: {len(prompts)} search prompts\n\n"
        
        result_text += "SEARCH PROMPTS:\n"
        result_text += "-" * 20 + "\n"
        
        for i, prompt in enumerate(prompts, 1):
            result_text += f"{i}. {prompt}\n\n"
        
        result_text += "USAGE SUGGESTIONS:\n"
        result_text += "-" * 20 + "\n"
        result_text += "• Use these prompts with jina_deepsearch_research tool\n"
        result_text += "• Copy prompts to external search engines or research databases\n"
        result_text += "• Modify prompts to focus on specific aspects of your analysis\n"
        result_text += "• Combine multiple search results for comprehensive insights\n\n"
        
        result_text += f"✨ Ready to enhance your data analysis with external research!"
        
        return TextContent(type="text", text=result_text)
        
    except LidaMcpError as e:
        return TextContent(type="text", text=f"Error: {str(e)}")
    except Exception as e:
        error_message = f"Failed to generate search prompts: {str(e)}"
        return TextContent(type="text", text=error_message)


def generate_fallback_prompts(dataset_info: dict, focus: str, num_needed: int) -> List[str]:
    """Generate fallback search prompts when LLM generation fails"""
    prompts = []
    
    # Extract key terms from dataset
    dataset_name = dataset_info['name'].replace('_', ' ').replace('-', ' ').title()
    numeric_cols = dataset_info['numeric_columns'][:3]
    categorical_cols = dataset_info['categorical_columns'][:3]
    keywords = dataset_info['keywords'][:5]
    
    base_prompts = {
        "industry": [
            f"Industry standards and benchmarks for {dataset_name} analysis",
            f"Market trends affecting {' '.join(keywords[:2])} performance",
            f"Best practices for {dataset_name} data interpretation",
            f"Regulatory requirements for {' '.join(keywords[:2])} reporting",
            f"Industry-specific KPIs for {dataset_name} evaluation"
        ],
        "trends": [
            f"Latest trends in {dataset_name} analytics and insights",
            f"Emerging patterns in {' '.join(keywords[:2])} data analysis",
            f"Future predictions for {dataset_name} metrics",
            f"Trend analysis methodologies for {' '.join(keywords[:2])}",
            f"Seasonal patterns affecting {dataset_name} performance"
        ],
        "benchmarks": [
            f"Performance benchmarks for {dataset_name} metrics",
            f"Comparative analysis standards for {' '.join(keywords[:2])}",
            f"Industry averages for {dataset_name} key indicators",
            f"Best-in-class performance metrics for {' '.join(keywords[:2])}",
            f"Benchmarking methodologies for {dataset_name} analysis"
        ],
        "research": [
            f"Academic research on {dataset_name} analysis methodologies",
            f"Statistical methods for {' '.join(keywords[:2])} data analysis",
            f"Research studies on {dataset_name} performance factors",
            f"Peer-reviewed papers on {' '.join(keywords[:2])} optimization",
            f"Data science approaches to {dataset_name} insights"
        ],
        "general": [
            f"What factors influence {dataset_name} performance and outcomes",
            f"How to interpret {' '.join(keywords[:2])} data effectively",
            f"Common challenges in {dataset_name} data analysis",
            f"External factors affecting {' '.join(keywords[:2])} metrics",
            f"Contextual information about {dataset_name} trends"
        ]
    }
    
    # Get prompts for the specified focus
    focus_prompts = base_prompts.get(focus, base_prompts["general"])
    
    # Add column-specific prompts if we have numeric columns
    if numeric_cols:
        col_name = numeric_cols[0].replace('_', ' ').title()
        focus_prompts.extend([
            f"Factors that impact {col_name} measurements and analysis",
            f"Industry standards for {col_name} performance evaluation"
        ])
    
    # Return the needed number of prompts
    return focus_prompts[:num_needed]


@mcp.tool(
    description="""Perform intelligent web research using Jina DeepSearch. This tool uses AI to search the web, analyze multiple sources, and provide comprehensive answers with citations.
    
    Args:
        query: The research question or search query
        reasoning_effort: "low", "medium", or "high" - NOTE: Always uses "low" for better performance
        stream: Whether to use streaming response (faster feedback)
        max_tokens: Maximum response tokens (optional)
        temperature: Sampling temperature for response generation (0.0-1.0)
        
    Returns:
        Comprehensive research result with answer, visited URLs, token usage, and metadata.
    """
)
@log_tool_execution("jina_deepsearch_research")
async def jina_deepsearch_research(
    query: str,
    reasoning_effort: Literal["low", "medium", "high"] = "low",
    stream: bool = False,  # Default to False for better reliability
    max_tokens: Optional[int] = None,
    temperature: Optional[float] = None
) -> TextContent:
    """Perform intelligent web research using Jina DeepSearch. 
    
    Note: reasoning_effort is always set to 'low' for better performance regardless of input.
    """
    try:
        import sys
        if not query.strip():
            log_step("jina_deepsearch_research", "Error: Empty query provided")
            return TextContent(type="text", text="Error: Query is required for research")
        
        log_step("jina_deepsearch_research", f"Starting Jina DeepSearch for query: {query[:50]}...")
        log_step("jina_deepsearch_research", f"Using reasoning_effort: low (always), stream: {stream}")
        log_api_call("jina_deepsearch_research", "Jina DeepSearch", "deepsearch.jina.ai")
        
        # Use the async context directly since we're already in an async function
        async with JinaDeepSearch(api_key=jina_api_key) as searcher:
            result = await searcher.search(
                query=query,
                reasoning_effort="low",  # Always use low for better performance
                stream=stream,
                max_tokens=max_tokens,
                temperature=temperature
            )
        
        log_step("jina_deepsearch_research", f"Jina DeepSearch completed, success: {result.get('success', False)}")
        
        if not result["success"]:
            error_msg = f"DeepSearch failed: {result.get('error', 'Unknown error')}"
            if result.get('details'):
                error_msg += f"\nDetails: {result['details']}"
            log_api_call("jina_deepsearch_research", "Jina DeepSearch", "deepsearch.jina.ai", 
                        success=False, error=error_msg)
            
            # Check for specific error conditions
            if "balance" in error_msg.lower() or "402" in error_msg:
                return TextContent(
                    type="text", 
                    text="Error: The Jina DeepSearch API account has insufficient balance to process this request. Please contact the administrator to recharge the API account or try again later."
                )
            elif "401" in error_msg:
                return TextContent(
                    type="text",
                    text="Error: Authentication failed with Jina DeepSearch API. Please check the API key configuration."
                )
            else:
                return TextContent(type="text", text=f"Error: {error_msg}")
        
        # Log successful API call with response details
        response_size = len(result.get("answer", ""))
        visited_urls_count = len(result.get("visited_urls", []))
        
        log_api_call("jina_deepsearch_research", "Jina DeepSearch", "deepsearch.jina.ai", 
                    success=True, response_size=response_size)
        log_step("jina_deepsearch_research", f"Response received: {response_size} chars, {visited_urls_count} URLs")
        
        # Format the response
        response_text = f"JINA DEEPSEARCH RESEARCH RESULTS\n"
        response_text += "=" * 50 + "\n\n"
        response_text += f"Query: {query}\n"
        response_text += f"Reasoning Effort: {reasoning_effort}\n"
        response_text += f"Stream Mode: {stream}\n\n"
        
        response_text += "ANSWER:\n"
        response_text += "-" * 10 + "\n"
        response_text += result["answer"] + "\n\n"
        
        # Add visited URLs if available
        if result.get("visited_urls"):
            response_text += "SOURCES CONSULTED:\n"
            response_text += "-" * 20 + "\n"
            for i, url in enumerate(result["visited_urls"], 1):
                response_text += f"{i}. {url}\n"
            response_text += "\n"
            log_step("jina_deepsearch_research", f"Sources consulted: {len(result['visited_urls'])} URLs")
        
        # Add token usage if available
        if result.get("token_usage"):
            usage = result["token_usage"]
            response_text += "TOKEN USAGE:\n"
            response_text += "-" * 15 + "\n"
            if "prompt_tokens" in usage:
                response_text += f"Prompt tokens: {usage['prompt_tokens']}\n"
            if "completion_tokens" in usage:
                response_text += f"Completion tokens: {usage['completion_tokens']}\n"
            if "total_tokens" in usage:
                response_text += f"Total tokens: {usage['total_tokens']}\n"
                log_step("jina_deepsearch_research", f"Token usage: {usage['total_tokens']} total tokens")
            response_text += "\n"
        
        # Add metadata if available
        if result.get("metadata"):
            metadata = result["metadata"]
            response_text += "METADATA:\n"
            response_text += "-" * 10 + "\n"
            for key, value in metadata.items():
                response_text += f"{key}: {value}\n"
            response_text += "\n"
            log_step("jina_deepsearch_research", f"Metadata received: {len(metadata)} fields")
        
        response_text += "Research completed successfully! ✅"
        
        log_step("jina_deepsearch_research", f"Research completed successfully - {len(response_text)} characters")
        
        # Handle large content
        from eda_mcp.utils import handle_large_text
        return TextContent(
            type="text",
            text=handle_large_text(response_text, max_length=12000, content_type="research results")
        )
        
    except Exception as e:
        import sys
        error_msg = f"Failed to perform DeepSearch research: {str(e)}"
        log_step("jina_deepsearch_research", f"Exception occurred: {error_msg}")
        log_api_call("jina_deepsearch_research", "Jina DeepSearch", "deepsearch.jina.ai", 
                    success=False, error=error_msg)
        return TextContent(type="text", text=f"Error: {error_msg}")


@mcp.tool(
    description="""Perform comprehensive Exploratory Data Analysis (EDA) on a dataset. This tool provides basic to advanced data exploration capabilities based on user requirements.

    The tool analyzes datasets and provides structured results including:
    - Basic statistics and data profiling
    - Data quality assessment with scoring
    - Missing value and duplicate analysis
    - Outlier detection and analysis
    - Actionable recommendations for next steps

    Analysis Types:
    - "basic": Standard statistical analysis and data profiling
    - "advanced": In-depth analysis with relationships and patterns
    - "custom": AI-powered custom analysis focused on ONE specific goal/question

    Args:
        file_path: Path to the dataset file to analyze
        analysis_type: Type of analysis - "basic", "advanced", or "custom"
        analysis_depth: Depth of analysis - "quick", "standard", or "comprehensive"
        target_columns: Specific columns to focus analysis on (optional)
        analysis_goals: For custom analysis, provide ONE specific goal/question to analyze (e.g., "Analyze quarterly sales trends")
        include_visualizations: Whether to generate visualizations (not yet implemented)
        max_execution_time: Maximum time to spend on analysis in seconds (30-1800)
        sample_size: Sample size for large datasets (optional, minimum 100)

    Returns:
        Comprehensive EDA results with statistics, quality assessment, and recommendations.
        For custom analysis: Returns both the generated Python code and execution results.
    """
)
@log_tool_execution("perform_eda")
def perform_eda(
    file_path: str,
    analysis_type: Literal["basic", "advanced", "custom"] = "basic",
    analysis_depth: Literal["quick", "standard", "comprehensive"] = "standard",
    target_columns: Optional[List[str]] = None,
    analysis_goals: Optional[List[str]] = None,
    include_visualizations: bool = False,  # Set to False until visualization is implemented
    max_execution_time: Optional[int] = 300,
    sample_size: Optional[int] = None,
) -> TextContent:
    """Perform comprehensive EDA on a dataset"""
    try:
        log_step("perform_eda", f"🔍 Starting EDA analysis for: {file_path}")
        log_step("perform_eda", f"📊 Analysis type: {analysis_type}, depth: {analysis_depth}")
        
        if analysis_goals:
            log_step("perform_eda", f"🎯 Analysis goals ({len(analysis_goals)}): {analysis_goals}")
        
        if target_columns:
            log_step("perform_eda", f"📍 Target columns ({len(target_columns)}): {target_columns}")
        
        # Import EDA components
        log_step("perform_eda", "📦 Importing EDA components")
        from eda_mcp.eda_engine import EDAEngine, BasicAnalysisModule, DataQualityModule, AdvancedAnalysisModule
        
        # Validate inputs
        if max_execution_time and not (30 <= max_execution_time <= 1800):
            log_step("perform_eda", f"❌ Invalid max_execution_time: {max_execution_time}")
            return TextContent(type="text", text="Error: max_execution_time must be between 30 and 1800 seconds")
        
        if sample_size and sample_size < 100:
            log_step("perform_eda", f"❌ Invalid sample_size: {sample_size}")
            return TextContent(type="text", text="Error: sample_size must be at least 100")
        
        log_step("perform_eda", "✅ Input validation passed")
        
        # Create EDA request
        log_step("perform_eda", "📋 Creating EDA request object")
        eda_request = EDARequest(
            file_path=file_path,
            analysis_type=analysis_type,
            analysis_depth=analysis_depth,
            target_columns=target_columns,
            analysis_goals=analysis_goals,
            include_visualizations=include_visualizations,
            max_execution_time=max_execution_time,
            sample_size=sample_size
        )
        
        # Initialize EDA engine and register modules
        log_step("perform_eda", "🏗️ Initializing EDA engine")
        engine = EDAEngine()

        # Register modules based on analysis type
        if analysis_type == "custom":
            log_step("perform_eda", "🤖 Registering CustomAnalysisModule with LLM integration")
            # For custom analysis, register the CustomAnalysisModule with LLM integration
            from eda_mcp.eda_engine import CustomAnalysisModule
            engine.register_module(CustomAnalysisModule(text_gen=text_gen, default_model=default_model))
            log_step("perform_eda", f"🔧 Custom module configured with model: {default_model}")
        else:
            log_step("perform_eda", "📈 Registering traditional analysis modules")
            # For basic and advanced analysis, register traditional modules
            engine.register_module(BasicAnalysisModule())
            log_step("perform_eda", "✅ BasicAnalysisModule registered")
            
            engine.register_module(DataQualityModule())
            log_step("perform_eda", "✅ DataQualityModule registered")

            if analysis_type == "advanced":
                engine.register_module(AdvancedAnalysisModule())
                log_step("perform_eda", "✅ AdvancedAnalysisModule registered")
        
        log_step("perform_eda", "🎛️ EDA engine initialized with all required modules")
        
        # Perform analysis
        log_step("perform_eda", "🚀 Starting EDA analysis execution")
        result = engine.perform_analysis(eda_request)
        
        log_step("perform_eda", f"✅ EDA analysis completed successfully in {result.execution_time:.2f} seconds")
        log_step("perform_eda", f"📊 Result contains: {len(result.analysis_metadata.get('modules_executed', []))} executed modules")
        
        # Check for any errors in modules
        errors = result.analysis_metadata.get('errors', [])
        if errors:
            log_step("perform_eda", f"⚠️ Some modules had errors: {errors}")
        else:
            log_step("perform_eda", "✅ All modules executed without errors")
        
        # Log summary statistics
        log_step("perform_eda", f"📈 Dataset summary: {result.dataset_info.shape[0]} rows, {result.dataset_info.shape[1]} columns")
        log_step("perform_eda", f"🔍 Data quality score: {result.data_quality.quality_score:.1f}/100")
        log_step("perform_eda", f"📋 Recommendations generated: {len(result.recommendations.next_steps)} next steps")
        
        # Format results for display
        log_step("perform_eda", "📝 Formatting results for display")
        response_text = "# EDA ANALYSIS RESULTS\n"
        response_text += "=" * 50 + "\n\n"
        
        # Dataset Information
        response_text += "## Dataset Information\n"
        response_text += f"**Name:** {result.dataset_info.name}\n"
        response_text += f"**Shape:** {result.dataset_info.shape[0]} rows × {result.dataset_info.shape[1]} columns\n"
        response_text += f"**Size:** {result.dataset_info.size_mb:.2f} MB\n"
        response_text += f"**Columns:** {', '.join(result.dataset_info.columns[:10])}"
        if len(result.dataset_info.columns) > 10:
            response_text += f" ... and {len(result.dataset_info.columns) - 10} more\n"
        else:
            response_text += "\n"
        response_text += "\n"
        
        # Data Quality Summary
        response_text += "## Data Quality Assessment\n"
        response_text += f"**Quality Score:** {result.data_quality.quality_score:.1f}/100\n"
        response_text += f"**Missing Values:** {result.data_quality.missing_values.get('total_missing', 0)} "
        response_text += f"({result.data_quality.missing_values.get('missing_percentage', 0):.1f}%)\n"
        response_text += f"**Duplicate Rows:** {result.data_quality.duplicates.get('duplicate_rows', {}).get('count', 0)} "
        response_text += f"({result.data_quality.duplicates.get('duplicate_rows', {}).get('percentage', 0):.1f}%)\n"
        
        # Issues
        if result.data_quality.issues:
            response_text += f"**Issues Found:** {len(result.data_quality.issues)}\n"
            for issue in result.data_quality.issues[:5]:  # Show top 5 issues
                response_text += f"  - {issue['severity'].upper()}: {issue['description']}\n"
            if len(result.data_quality.issues) > 5:
                response_text += f"  ... and {len(result.data_quality.issues) - 5} more issues\n"
        response_text += "\n"
        
        # Basic Statistics Summary
        response_text += "## Statistical Summary\n"
        numerical_cols = len(result.basic_stats.numerical)
        categorical_cols = len(result.basic_stats.categorical)
        response_text += f"**Numerical Columns:** {numerical_cols}\n"
        response_text += f"**Categorical Columns:** {categorical_cols}\n"
        
        # Show summary for a few key numerical columns
        if result.basic_stats.numerical:
            response_text += "\n**Key Numerical Statistics:**\n"
            for i, (col, stats) in enumerate(list(result.basic_stats.numerical.items())[:3]):
                response_text += f"  - **{col}:** Mean={stats['mean']:.2f}, Std={stats['std']:.2f}, "
                response_text += f"Missing={stats['missing_percentage']:.1f}%\n"
            if len(result.basic_stats.numerical) > 3:
                response_text += f"  ... and {len(result.basic_stats.numerical) - 3} more numerical columns\n"
        
        # Show summary for a few key categorical columns
        if result.basic_stats.categorical:
            response_text += "\n**Key Categorical Statistics:**\n"
            for i, (col, stats) in enumerate(list(result.basic_stats.categorical.items())[:3]):
                response_text += f"  - **{col}:** {stats['unique_count']} unique values, "
                response_text += f"Missing={stats['missing_percentage']:.1f}%\n"
            if len(result.basic_stats.categorical) > 3:
                response_text += f"  ... and {len(result.basic_stats.categorical) - 3} more categorical columns\n"
        
        response_text += "\n"
        
        # Goal-Specific Analysis Results (for advanced/custom analysis)
        if analysis_goals and hasattr(result, 'relationships') and result.relationships:
            # Check if we have advanced analysis results
            analysis_results = result.analysis_metadata.get('modules_executed', [])
            if 'advanced' in analysis_results:
                log_step("perform_eda", "📋 Adding goal-specific analysis results to output")
                response_text += "## Goal-Specific Analysis Results\n"
                
                # Try to access goal-specific results from the advanced module
                # This is a bit of a hack since we need to access the raw results
                # In a production system, we'd want to structure this better
                try:
                    # We'll add goal-specific formatting here
                    goal_count = len(analysis_goals)
                    response_text += f"**Analysis Goals Addressed:** {goal_count}\n\n"
                    
                    for i, goal in enumerate(analysis_goals[:3], 1):  # Show first 3 goals
                        response_text += f"**Goal {i}:** {goal}\n"
                        
                        # Add specific findings based on goal type
                        goal_lower = goal.lower()
                        if "correlation" in goal_lower:
                            if result.relationships.correlations:
                                strong_corrs = []
                                # Extract correlation info (simplified)
                                response_text += f"  - **Finding:** Correlation analysis completed\n"
                                if "quarterly" in goal_lower and "annual" in goal_lower:
                                    response_text += f"  - **Focus:** Quarterly vs Annual data alignment analysis\n"
                        
                        elif "outlier" in goal_lower:
                            response_text += f"  - **Finding:** Outlier detection analysis completed\n"
                            if "production" in goal_lower:
                                response_text += f"  - **Focus:** Production data anomaly detection\n"
                        
                        elif "trend" in goal_lower:
                            response_text += f"  - **Finding:** Trend consistency analysis completed\n"
                            if "quarterly" in goal_lower:
                                response_text += f"  - **Focus:** Quarterly data trend validation\n"
                        
                        elif "time series" in goal_lower or "pattern" in goal_lower:
                            response_text += f"  - **Finding:** Time series pattern analysis completed\n"
                            response_text += f"  - **Focus:** Temporal pattern identification across forecast periods\n"
                        
                        elif "statistical validation" in goal_lower or "integrity" in goal_lower:
                            response_text += f"  - **Finding:** Data integrity validation completed\n"
                            response_text += f"  - **Focus:** Statistical validation of forecasting data quality\n"
                        
                        response_text += "\n"
                    
                    if len(analysis_goals) > 3:
                        response_text += f"  ... and {len(analysis_goals) - 3} more goals analyzed\n\n"
                
                except Exception as e:
                    log_step("perform_eda", f"⚠️ Issue formatting goal-specific results: {str(e)}")
                    response_text += f"**Note:** Goal-specific analysis completed but detailed results formatting encountered an issue.\n\n"
        
        # Advanced Analysis Summary (if available)
        if analysis_type in ["advanced", "custom"] and result.relationships:
            log_step("perform_eda", "📈 Adding advanced analysis summary to output")
            response_text += "## Advanced Analysis Summary\n"
            
            # Correlation findings
            if result.relationships.correlations:
                response_text += "**Correlation Analysis:**\n"
                response_text += "  - Pearson correlation analysis completed\n"
                response_text += "  - Spearman rank correlation analysis completed\n"
                response_text += "  - Strong correlations identified and analyzed\n"
            
            # Feature interactions
            if result.relationships.feature_interactions:
                interaction_count = len(result.relationships.feature_interactions)
                response_text += f"**Feature Interactions:** {interaction_count} potential interactions identified\n"
            
            response_text += "\n"

        # Custom Analysis Results (if available)
        if analysis_type == "custom":
            log_step("perform_eda", "🤖 Adding custom analysis results to output")
            response_text += "## Custom EDA Analysis Results\n"

            # Check if we have custom analysis results in the raw analysis data
            try:
                # Access the raw analysis results to get custom analysis data
                raw_results = getattr(result, '_raw_analysis_results', {})
                custom_results = raw_results.get('custom', {})

                if custom_results:
                    custom_analysis = custom_results.get('custom_analysis', {})

                    if custom_analysis.get('success', False):
                        log_step("perform_eda", "✅ Custom analysis was successful")
                        response_text += "**✅ Custom Analysis Status:** Successfully completed\n"

                        # Display generated code
                        generated_code = custom_analysis.get('generated_code', '')
                        if generated_code:
                            log_step("perform_eda", f"📝 Generated code length: {len(generated_code)} characters")
                            response_text += "\n**Generated EDA Code:**\n"
                            response_text += "```python\n"
                            response_text += generated_code
                            response_text += "\n```\n\n"

                        # Display execution results
                        execution_results = custom_analysis.get('execution_results', {})
                        if execution_results.get('success', False):
                            analysis_results = execution_results.get('analysis_results', {})
                            execution_time = execution_results.get('execution_time', 0)

                            log_step("perform_eda", f"⏱️ Code execution time: {execution_time:.2f} seconds")
                            log_step("perform_eda", f"📊 Analysis results categories: {len(analysis_results)}")

                            response_text += f"**Execution Time:** {execution_time:.2f} seconds\n"
                            response_text += f"**Analysis Categories:** {len(analysis_results)} result categories\n"

                            # Display key findings from analysis results
                            if analysis_results:
                                response_text += "\n**Key Findings:**\n"
                                for key, value in list(analysis_results.items())[:5]:  # Show first 5 categories
                                    if isinstance(value, dict) and value:
                                        response_text += f"  - **{key.replace('_', ' ').title()}:** Analysis completed\n"
                                    elif isinstance(value, (list, tuple)) and value:
                                        response_text += f"  - **{key.replace('_', ' ').title()}:** {len(value)} items found\n"
                                    elif isinstance(value, (int, float)):
                                        response_text += f"  - **{key.replace('_', ' ').title()}:** {value}\n"

                                if len(analysis_results) > 5:
                                    response_text += f"  ... and {len(analysis_results) - 5} more analysis categories\n"

                            # Display stdout output if available
                            stdout_output = execution_results.get('stdout_output', '').strip()
                            if stdout_output:
                                log_step("perform_eda", f"📄 Stdout output length: {len(stdout_output)} characters")
                                response_text += f"\n**Analysis Output:**\n```\n{stdout_output[:500]}"
                                if len(stdout_output) > 500:
                                    response_text += "\n... (output truncated)"
                                response_text += "\n```\n"

                        else:
                            log_step("perform_eda", "❌ Custom code execution failed")
                            response_text += "**❌ Code Execution:** Failed\n"
                            error_msg = execution_results.get('error_message', 'Unknown error')
                            response_text += f"**Error:** {error_msg}\n"

                    else:
                        log_step("perform_eda", "❌ Custom analysis failed")
                        response_text += "**❌ Custom Analysis Status:** Failed\n"
                        error_msg = custom_analysis.get('error_message', 'Unknown error')
                        response_text += f"**Error:** {error_msg}\n"

                        # Check if escalated to advanced analysis
                        execution_results = custom_analysis.get('execution_results', {})
                        if execution_results.get('escalated_to_advanced', False):
                            log_step("perform_eda", "🔄 Escalated to advanced analysis fallback")
                            response_text += "**🔄 Fallback:** Escalated to advanced analysis mode\n"

                            # Show advanced analysis results if available
                            advanced_fallback = custom_results.get('advanced_fallback', {})
                            if advanced_fallback:
                                response_text += "**Advanced Analysis Fallback:** Completed successfully\n"

                else:
                    log_step("perform_eda", "⚠️ Custom analysis results not available in expected format")
                    response_text += "**Note:** Custom analysis results not available in expected format\n"

            except Exception as e:
                log_step("perform_eda", f"❌ Error formatting custom analysis results: {str(e)}")
                response_text += f"**Note:** Error formatting custom analysis results: {str(e)}\n"

            response_text += "\n"

        # Recommendations
        if result.recommendations:
            log_step("perform_eda", "💡 Adding recommendations to output")
            response_text += "## Recommendations\n"
            
            # Priority actions
            priority_actions = result.recommendations.priority_actions
            if priority_actions:
                response_text += "**Priority Actions:**\n"
                for action in priority_actions[:3]:
                    response_text += f"  - {action['action']}: {action['details']}\n"
            
            # Data cleaning
            data_cleaning = result.recommendations.data_cleaning
            if data_cleaning:
                response_text += "\n**Data Cleaning:**\n"
                for action in data_cleaning[:3]:
                    response_text += f"  - {action['action']}: {action['details']}\n"
            
            # Next steps
            next_steps = result.recommendations.next_steps
            if next_steps:
                response_text += "\n**Suggested Next Steps:**\n"
                for action in next_steps[:3]:
                    response_text += f"  - {action['action']}: {action['details']}\n"
        
        response_text += "\n"
        
        # Analysis Metadata
        response_text += "## Analysis Details\n"
        response_text += f"**Execution Time:** {result.execution_time:.2f} seconds\n"
        response_text += f"**Analysis Type:** {analysis_type}\n"
        response_text += f"**Analysis Depth:** {analysis_depth}\n"
        if result.analysis_metadata.get('errors'):
            response_text += f"**Errors:** {len(result.analysis_metadata['errors'])} modules had errors\n"
        
        response_text += "\n✅ EDA analysis completed successfully!"
        
        log_step("perform_eda", f"📝 Response formatting completed - {len(response_text)} characters")
        
        # Handle large content
        from eda_mcp.utils import handle_large_text
        final_text = handle_large_text(response_text, max_length=15000, content_type="EDA results")
        
        log_step("perform_eda", f"🎉 EDA analysis workflow completed successfully!")
        
        return TextContent(
            type="text",
            text=final_text
        )
        
    except Exception as e:
        import sys
        error_msg = f"EDA analysis failed: {str(e)}"
        log_step("perform_eda", f"💥 Exception occurred: {error_msg}")
        logger.error(f"perform_eda error details: {traceback.format_exc()}")
        print(f"[DEBUG] EDA Error: {error_msg}", file=sys.stderr)
        print(f"[DEBUG] Error type: {type(e).__name__}", file=sys.stderr)
        return TextContent(type="text", text=f"Error: {error_msg}")


def main():
    """Run the EDA MCP server"""
    import sys
    
    # Initialize logger
    main_logger = get_logger().main_logger
    
    main_logger.info("Starting EDA MCP server...")
    main_logger.info(f"Version: {__version__}")
    main_logger.info(f"Model: {default_model}")
    main_logger.info(f"Base path: {base_path or 'Not set'}")
    
    # Count available tools by checking the mcp object's registered functions
    try:
        tool_count = len([attr for attr in dir(mcp) if attr.startswith('_tool_')])
        main_logger.info(f"Available tools: {tool_count} tools registered")
    except Exception as e:
        main_logger.info(f"Available tools: Unable to count tools ({str(e)})")
    
    # Log system information
    main_logger.info(f"Python version: {sys.version}")
    main_logger.info(f"Working directory: {os.getcwd()}")
    
    # Log environment variables (sanitized)
    env_vars = ['AZURE_OPENAI_ENDPOINT', 'AZURE_OPENAI_DEPLOYMENT_NAME', 'EDA_MCP_BASE_PATH']
    for var in env_vars:
        value = os.getenv(var)
        if value:
            # Show only first 20 characters for security
            main_logger.info(f"{var}: {value[:20]}...")
        else:
            main_logger.info(f"{var}: Not set")
    
    main_logger.info("EDA MCP Server initialization complete")
    main_logger.info("="*60)
    
    try:
        mcp.run()
    except KeyboardInterrupt:
        main_logger.info("Server stopped by user (Ctrl+C)")
    except Exception as e:
        main_logger.error(f"Server crashed: {str(e)}")
        raise
    finally:
        main_logger.info("EDA MCP Server shutting down...")
        cleanup_logs()


if __name__ == "__main__":
    main()
