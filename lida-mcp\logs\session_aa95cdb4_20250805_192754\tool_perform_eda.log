[2025-08-05 19:29:22.046] [aa95cdb4] [0.002s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:29:22.048] [aa95cdb4] [0.004s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Perform comprehensive time series analysis including trend analysis, seasonality detection, forecasting accuracy over time, and temporal patterns in order quantities and forecasts"
  ],
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 19:29:22.049] [aa95cdb4] [0.005s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:29:22.050] [aa95cdb4] [0.006s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Analysis type: custom, depth: comprehensive
[2025-08-05 19:29:22.051] [aa95cdb4] [0.007s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎯 Analysis goals (1): ['Perform comprehensive time series analysis including trend analysis, seasonality detection, forecasting accuracy over time, and temporal patterns in order quantities and forecasts']
[2025-08-05 19:29:22.051] [aa95cdb4] [0.007s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📦 Importing EDA components
[2025-08-05 19:29:22.144] [aa95cdb4] [0.100s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ Input validation passed
[2025-08-05 19:29:22.145] [aa95cdb4] [0.101s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Creating EDA request object
[2025-08-05 19:29:22.146] [aa95cdb4] [0.102s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🏗️ Initializing EDA engine
[2025-08-05 19:29:22.146] [aa95cdb4] [0.102s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Registering CustomAnalysisModule with LLM integration
[2025-08-05 19:29:22.149] [aa95cdb4] [0.105s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔧 Custom module configured with model: gpt-4.1
[2025-08-05 19:29:22.149] [aa95cdb4] [0.105s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎛️ EDA engine initialized with all required modules
[2025-08-05 19:29:22.149] [aa95cdb4] [0.105s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🚀 Starting EDA analysis execution
[2025-08-05 19:29:47.341] [aa95cdb4] [25.297s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ EDA analysis completed successfully in 25.19 seconds
[2025-08-05 19:29:47.341] [aa95cdb4] [25.298s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Result contains: 1 executed modules
[2025-08-05 19:29:47.342] [aa95cdb4] [25.298s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ All modules executed without errors
[2025-08-05 19:29:47.342] [aa95cdb4] [25.298s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📈 Dataset summary: 174 rows, 19 columns
[2025-08-05 19:29:47.342] [aa95cdb4] [25.299s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Data quality score: 0.0/100
[2025-08-05 19:29:47.343] [aa95cdb4] [25.299s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Recommendations generated: 0 next steps
[2025-08-05 19:29:47.343] [aa95cdb4] [25.299s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Formatting results for display
[2025-08-05 19:29:47.344] [aa95cdb4] [25.300s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📈 Adding advanced analysis summary to output
[2025-08-05 19:29:47.344] [aa95cdb4] [25.300s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Adding custom analysis results to output
[2025-08-05 19:29:47.344] [aa95cdb4] [25.300s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ❌ Custom analysis failed
[2025-08-05 19:29:47.345] [aa95cdb4] [25.301s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 💡 Adding recommendations to output
[2025-08-05 19:29:47.345] [aa95cdb4] [25.301s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Response formatting completed - 898 characters
[2025-08-05 19:29:47.345] [aa95cdb4] [25.301s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎉 EDA analysis workflow completed successfully!
[2025-08-05 19:29:47.346] [aa95cdb4] [25.302s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 25.297s
[2025-08-05 19:29:47.346] [aa95cdb4] [25.302s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:29:47.346] [aa95cdb4] [25.302s] INFO in eda_mcp_tool_perform_eda: Result length: 898 characters
[2025-08-05 19:29:52.388] [aa95cdb4] [30.344s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:29:52.388] [aa95cdb4] [30.344s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Analyze temporal trends in actual_order_quantity and forecast accuracy, calculate monthly aggregations, identify seasonal patterns, and compare forecast performance across different time periods"
  ],
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 19:29:52.389] [aa95cdb4] [30.345s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:29:52.389] [aa95cdb4] [30.345s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Analysis type: custom, depth: comprehensive
[2025-08-05 19:29:52.389] [aa95cdb4] [30.345s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎯 Analysis goals (1): ['Analyze temporal trends in actual_order_quantity and forecast accuracy, calculate monthly aggregations, identify seasonal patterns, and compare forecast performance across different time periods']
[2025-08-05 19:29:52.389] [aa95cdb4] [30.345s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📦 Importing EDA components
[2025-08-05 19:29:52.389] [aa95cdb4] [30.345s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ Input validation passed
[2025-08-05 19:29:52.389] [aa95cdb4] [30.345s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Creating EDA request object
[2025-08-05 19:29:52.389] [aa95cdb4] [30.345s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🏗️ Initializing EDA engine
[2025-08-05 19:29:52.389] [aa95cdb4] [30.346s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Registering CustomAnalysisModule with LLM integration
[2025-08-05 19:29:52.390] [aa95cdb4] [30.346s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔧 Custom module configured with model: gpt-4.1
[2025-08-05 19:29:52.390] [aa95cdb4] [30.346s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎛️ EDA engine initialized with all required modules
[2025-08-05 19:29:52.390] [aa95cdb4] [30.346s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🚀 Starting EDA analysis execution
[2025-08-05 19:33:52.130] [aa95cdb4] [270.086s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ EDA analysis completed successfully in 239.74 seconds
[2025-08-05 19:33:52.130] [aa95cdb4] [270.086s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Result contains: 1 executed modules
[2025-08-05 19:33:52.130] [aa95cdb4] [270.087s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ All modules executed without errors
[2025-08-05 19:33:52.131] [aa95cdb4] [270.087s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📈 Dataset summary: 174 rows, 19 columns
[2025-08-05 19:33:52.131] [aa95cdb4] [270.087s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Data quality score: 0.0/100
[2025-08-05 19:33:52.131] [aa95cdb4] [270.087s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Recommendations generated: 1 next steps
[2025-08-05 19:33:52.131] [aa95cdb4] [270.087s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Formatting results for display
[2025-08-05 19:33:52.131] [aa95cdb4] [270.087s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📈 Adding advanced analysis summary to output
[2025-08-05 19:33:52.131] [aa95cdb4] [270.088s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Adding custom analysis results to output
[2025-08-05 19:33:52.131] [aa95cdb4] [270.088s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ Custom analysis was successful
[2025-08-05 19:33:52.132] [aa95cdb4] [270.088s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Generated code length: 8390 characters
[2025-08-05 19:33:52.132] [aa95cdb4] [270.088s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ⏱️ Code execution time: 0.24 seconds
[2025-08-05 19:33:52.132] [aa95cdb4] [270.088s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Analysis results categories: 3
[2025-08-05 19:33:52.133] [aa95cdb4] [270.089s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 💡 Adding recommendations to output
[2025-08-05 19:33:52.133] [aa95cdb4] [270.089s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Response formatting completed - 12623 characters
[2025-08-05 19:33:52.133] [aa95cdb4] [270.090s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎉 EDA analysis workflow completed successfully!
[2025-08-05 19:33:52.135] [aa95cdb4] [270.091s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 239.746s
[2025-08-05 19:33:52.135] [aa95cdb4] [270.091s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:33:52.135] [aa95cdb4] [270.091s] INFO in eda_mcp_tool_perform_eda: Result length: 12623 characters
[2025-08-05 19:33:52.184] [aa95cdb4] [270.140s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:33:52.185] [aa95cdb4] [270.141s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Perform detailed time series analysis including decomposition of trends and seasonality, autocorrelation analysis, forecast error analysis over time, moving averages, and time-based performance metrics across different time periods"
  ],
  "include_visualizations": false,
  "max_execution_time": 800,
  "sample_size": null
}
[2025-08-05 19:33:52.185] [aa95cdb4] [270.142s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:33:52.186] [aa95cdb4] [270.142s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Analysis type: custom, depth: comprehensive
[2025-08-05 19:33:52.186] [aa95cdb4] [270.142s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎯 Analysis goals (1): ['Perform detailed time series analysis including decomposition of trends and seasonality, autocorrelation analysis, forecast error analysis over time, moving averages, and time-based performance metrics across different time periods']
[2025-08-05 19:33:52.186] [aa95cdb4] [270.142s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📦 Importing EDA components
[2025-08-05 19:33:52.186] [aa95cdb4] [270.142s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ Input validation passed
[2025-08-05 19:33:52.186] [aa95cdb4] [270.142s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Creating EDA request object
[2025-08-05 19:33:52.186] [aa95cdb4] [270.142s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🏗️ Initializing EDA engine
[2025-08-05 19:33:52.186] [aa95cdb4] [270.142s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Registering CustomAnalysisModule with LLM integration
[2025-08-05 19:33:52.186] [aa95cdb4] [270.142s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔧 Custom module configured with model: gpt-4.1
[2025-08-05 19:33:52.186] [aa95cdb4] [270.142s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎛️ EDA engine initialized with all required modules
[2025-08-05 19:33:52.186] [aa95cdb4] [270.143s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🚀 Starting EDA analysis execution
[2025-08-05 19:34:23.550] [aa95cdb4] [301.506s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ EDA analysis completed successfully in 31.36 seconds
[2025-08-05 19:34:23.550] [aa95cdb4] [301.506s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Result contains: 1 executed modules
[2025-08-05 19:34:23.550] [aa95cdb4] [301.507s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ All modules executed without errors
[2025-08-05 19:34:23.551] [aa95cdb4] [301.507s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📈 Dataset summary: 174 rows, 19 columns
[2025-08-05 19:34:23.551] [aa95cdb4] [301.507s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Data quality score: 0.0/100
[2025-08-05 19:34:23.551] [aa95cdb4] [301.507s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Recommendations generated: 1 next steps
[2025-08-05 19:34:23.551] [aa95cdb4] [301.507s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Formatting results for display
[2025-08-05 19:34:23.551] [aa95cdb4] [301.507s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📈 Adding advanced analysis summary to output
[2025-08-05 19:34:23.551] [aa95cdb4] [301.507s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Adding custom analysis results to output
[2025-08-05 19:34:23.551] [aa95cdb4] [301.507s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ Custom analysis was successful
[2025-08-05 19:34:23.551] [aa95cdb4] [301.508s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Generated code length: 8451 characters
[2025-08-05 19:34:23.552] [aa95cdb4] [301.508s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ⏱️ Code execution time: 0.10 seconds
[2025-08-05 19:34:23.552] [aa95cdb4] [301.508s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Analysis results categories: 3
[2025-08-05 19:34:23.552] [aa95cdb4] [301.508s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 💡 Adding recommendations to output
[2025-08-05 19:34:23.552] [aa95cdb4] [301.508s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Response formatting completed - 12272 characters
[2025-08-05 19:34:23.552] [aa95cdb4] [301.508s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎉 EDA analysis workflow completed successfully!
[2025-08-05 19:34:23.552] [aa95cdb4] [301.509s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 31.367s
[2025-08-05 19:34:23.553] [aa95cdb4] [301.509s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:34:23.553] [aa95cdb4] [301.509s] INFO in eda_mcp_tool_perform_eda: Result length: 12272 characters
[2025-08-05 19:34:23.575] [aa95cdb4] [301.531s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:34:23.576] [aa95cdb4] [301.532s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "standard",
  "target_columns": null,
  "analysis_goals": [
    "Focus on time series patterns in reference_date and month columns, analyze order quantity trends over time, and calculate basic forecast accuracy metrics"
  ],
  "include_visualizations": false,
  "max_execution_time": 300,
  "sample_size": null
}
[2025-08-05 19:34:23.576] [aa95cdb4] [301.532s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:34:23.576] [aa95cdb4] [301.532s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Analysis type: custom, depth: standard
[2025-08-05 19:34:23.576] [aa95cdb4] [301.532s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎯 Analysis goals (1): ['Focus on time series patterns in reference_date and month columns, analyze order quantity trends over time, and calculate basic forecast accuracy metrics']
[2025-08-05 19:34:23.576] [aa95cdb4] [301.532s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📦 Importing EDA components
[2025-08-05 19:34:23.577] [aa95cdb4] [301.533s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ Input validation passed
[2025-08-05 19:34:23.577] [aa95cdb4] [301.533s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Creating EDA request object
[2025-08-05 19:34:23.577] [aa95cdb4] [301.533s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🏗️ Initializing EDA engine
[2025-08-05 19:34:23.577] [aa95cdb4] [301.533s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Registering CustomAnalysisModule with LLM integration
[2025-08-05 19:34:23.578] [aa95cdb4] [301.534s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔧 Custom module configured with model: gpt-4.1
[2025-08-05 19:34:23.578] [aa95cdb4] [301.534s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎛️ EDA engine initialized with all required modules
[2025-08-05 19:34:23.578] [aa95cdb4] [301.534s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🚀 Starting EDA analysis execution
[2025-08-05 19:34:36.455] [aa95cdb4] [314.411s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ EDA analysis completed successfully in 12.88 seconds
[2025-08-05 19:34:36.455] [aa95cdb4] [314.411s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Result contains: 1 executed modules
[2025-08-05 19:34:36.455] [aa95cdb4] [314.411s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ All modules executed without errors
[2025-08-05 19:34:36.455] [aa95cdb4] [314.411s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📈 Dataset summary: 174 rows, 19 columns
[2025-08-05 19:34:36.455] [aa95cdb4] [314.411s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Data quality score: 0.0/100
[2025-08-05 19:34:36.455] [aa95cdb4] [314.411s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Recommendations generated: 1 next steps
[2025-08-05 19:34:36.455] [aa95cdb4] [314.412s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Formatting results for display
[2025-08-05 19:34:36.456] [aa95cdb4] [314.412s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📈 Adding advanced analysis summary to output
[2025-08-05 19:34:36.456] [aa95cdb4] [314.412s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Adding custom analysis results to output
[2025-08-05 19:34:36.456] [aa95cdb4] [314.412s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ Custom analysis was successful
[2025-08-05 19:34:36.456] [aa95cdb4] [314.412s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Generated code length: 5230 characters
[2025-08-05 19:34:36.456] [aa95cdb4] [314.412s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ⏱️ Code execution time: 0.02 seconds
[2025-08-05 19:34:36.456] [aa95cdb4] [314.412s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Analysis results categories: 3
[2025-08-05 19:34:36.456] [aa95cdb4] [314.412s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 💡 Adding recommendations to output
[2025-08-05 19:34:36.457] [aa95cdb4] [314.413s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Response formatting completed - 7708 characters
[2025-08-05 19:34:36.457] [aa95cdb4] [314.413s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎉 EDA analysis workflow completed successfully!
[2025-08-05 19:34:36.457] [aa95cdb4] [314.413s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 12.881s
[2025-08-05 19:34:36.457] [aa95cdb4] [314.413s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:34:36.457] [aa95cdb4] [314.413s] INFO in eda_mcp_tool_perform_eda: Result length: 7708 characters
[2025-08-05 20:07:58.798] [aa95cdb4] [2316.755s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 20:07:58.806] [aa95cdb4] [2316.763s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Perform advanced time series modeling including decomposition analysis, autocorrelation analysis, forecast model validation, residual analysis, and future trend projections based on the identified growth patterns"
  ],
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 20:07:58.811] [aa95cdb4] [2316.767s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 20:07:58.813] [aa95cdb4] [2316.769s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Analysis type: custom, depth: comprehensive
[2025-08-05 20:07:58.817] [aa95cdb4] [2316.773s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎯 Analysis goals (1): ['Perform advanced time series modeling including decomposition analysis, autocorrelation analysis, forecast model validation, residual analysis, and future trend projections based on the identified growth patterns']
[2025-08-05 20:07:58.819] [aa95cdb4] [2316.775s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📦 Importing EDA components
[2025-08-05 20:07:58.823] [aa95cdb4] [2316.779s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ Input validation passed
[2025-08-05 20:07:58.823] [aa95cdb4] [2316.779s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Creating EDA request object
[2025-08-05 20:07:58.824] [aa95cdb4] [2316.780s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🏗️ Initializing EDA engine
[2025-08-05 20:07:58.824] [aa95cdb4] [2316.780s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Registering CustomAnalysisModule with LLM integration
[2025-08-05 20:07:58.827] [aa95cdb4] [2316.783s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔧 Custom module configured with model: gpt-4.1
[2025-08-05 20:07:58.828] [aa95cdb4] [2316.784s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎛️ EDA engine initialized with all required modules
[2025-08-05 20:07:58.828] [aa95cdb4] [2316.784s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🚀 Starting EDA analysis execution
[2025-08-05 20:08:20.087] [aa95cdb4] [2338.043s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ EDA analysis completed successfully in 21.26 seconds
[2025-08-05 20:08:20.087] [aa95cdb4] [2338.043s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Result contains: 1 executed modules
[2025-08-05 20:08:20.087] [aa95cdb4] [2338.043s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ All modules executed without errors
[2025-08-05 20:08:20.087] [aa95cdb4] [2338.043s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📈 Dataset summary: 174 rows, 19 columns
[2025-08-05 20:08:20.087] [aa95cdb4] [2338.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Data quality score: 0.0/100
[2025-08-05 20:08:20.087] [aa95cdb4] [2338.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Recommendations generated: 1 next steps
[2025-08-05 20:08:20.088] [aa95cdb4] [2338.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Formatting results for display
[2025-08-05 20:08:20.088] [aa95cdb4] [2338.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📈 Adding advanced analysis summary to output
[2025-08-05 20:08:20.088] [aa95cdb4] [2338.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Adding custom analysis results to output
[2025-08-05 20:08:20.088] [aa95cdb4] [2338.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ Custom analysis was successful
[2025-08-05 20:08:20.088] [aa95cdb4] [2338.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Generated code length: 6253 characters
[2025-08-05 20:08:20.088] [aa95cdb4] [2338.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ⏱️ Code execution time: 0.14 seconds
[2025-08-05 20:08:20.088] [aa95cdb4] [2338.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Analysis results categories: 3
[2025-08-05 20:08:20.088] [aa95cdb4] [2338.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 💡 Adding recommendations to output
[2025-08-05 20:08:20.088] [aa95cdb4] [2338.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Response formatting completed - 7955 characters
[2025-08-05 20:08:20.088] [aa95cdb4] [2338.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎉 EDA analysis workflow completed successfully!
[2025-08-05 20:08:20.088] [aa95cdb4] [2338.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 21.278s
[2025-08-05 20:08:20.088] [aa95cdb4] [2338.045s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 20:08:20.088] [aa95cdb4] [2338.045s] INFO in eda_mcp_tool_perform_eda: Result length: 7955 characters
[2025-08-05 20:08:25.996] [aa95cdb4] [2343.952s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 20:08:25.996] [aa95cdb4] [2343.952s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Perform forecast model optimization analysis including bias detection, forecast horizon analysis, model performance by profit center and carline, and statistical model comparison between SW and customer forecasts"
  ],
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.953s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.953s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Analysis type: custom, depth: comprehensive
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.953s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎯 Analysis goals (1): ['Perform forecast model optimization analysis including bias detection, forecast horizon analysis, model performance by profit center and carline, and statistical model comparison between SW and customer forecasts']
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.953s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📦 Importing EDA components
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.953s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ Input validation passed
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.953s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Creating EDA request object
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.953s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🏗️ Initializing EDA engine
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.953s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Registering CustomAnalysisModule with LLM integration
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.953s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔧 Custom module configured with model: gpt-4.1
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.953s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎛️ EDA engine initialized with all required modules
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.953s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🚀 Starting EDA analysis execution
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.260s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ EDA analysis completed successfully in 19.31 seconds
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.260s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Result contains: 1 executed modules
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.260s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ All modules executed without errors
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.260s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📈 Dataset summary: 174 rows, 19 columns
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.260s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🔍 Data quality score: 0.0/100
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.260s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📋 Recommendations generated: 1 next steps
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.260s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Formatting results for display
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.260s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📈 Adding advanced analysis summary to output
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.260s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🤖 Adding custom analysis results to output
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.260s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ✅ Custom analysis was successful
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.260s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Generated code length: 8005 characters
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.261s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: ⏱️ Code execution time: 0.10 seconds
[2025-08-05 20:08:45.305] [aa95cdb4] [2363.261s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📊 Analysis results categories: 3
[2025-08-05 20:08:45.305] [aa95cdb4] [2363.261s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 💡 Adding recommendations to output
[2025-08-05 20:08:45.305] [aa95cdb4] [2363.261s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 📝 Response formatting completed - 12274 characters
[2025-08-05 20:08:45.305] [aa95cdb4] [2363.261s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: 🎉 EDA analysis workflow completed successfully!
[2025-08-05 20:08:45.305] [aa95cdb4] [2363.261s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 19.308s
[2025-08-05 20:08:45.305] [aa95cdb4] [2363.261s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 20:08:45.305] [aa95cdb4] [2363.261s] INFO in eda_mcp_tool_perform_eda: Result length: 12274 characters
