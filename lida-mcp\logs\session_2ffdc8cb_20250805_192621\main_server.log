[2025-08-05 19:26:28.258] [2ffdc8cb] [0.001s] INFO in eda_mcp_server: EDA MCP Server session started - ID: 2ffdc8cb
[2025-08-05 19:26:28.259] [2ffdc8cb] [0.001s] INFO in eda_mcp_server: Log directory: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp\logs\session_2ffdc8cb_20250805_192621
[2025-08-05 19:26:28.259] [2ffdc8cb] [0.001s] INFO in eda_mcp_server: Initializing EDA MCP Server
[2025-08-05 19:26:28.259] [2ffdc8cb] [0.002s] INFO in eda_mcp_server: Configuration loaded - endpoint: https://sivanithish-test.openai.azure.com/, deployment: gpt-4o
[2025-08-05 19:26:28.260] [2ffdc8cb] [0.002s] INFO in eda_mcp_server: Jina API key configured: jina_704d29e58ed547c...
[2025-08-05 19:26:28.260] [2ffdc8cb] [0.002s] INFO in eda_mcp_server: Configuring Azure OpenAI client
[2025-08-05 19:26:29.214] [2ffdc8cb] [0.957s] INFO in eda_mcp_server: Azure OpenAI configured with model: gpt-4o
[2025-08-05 19:26:29.287] [2ffdc8cb] [1.029s] INFO in eda_mcp_server: Starting EDA MCP server...
[2025-08-05 19:26:29.288] [2ffdc8cb] [1.030s] INFO in eda_mcp_server: Version: 0.1.0
[2025-08-05 19:26:29.288] [2ffdc8cb] [1.030s] INFO in eda_mcp_server: Model: gpt-4o
[2025-08-05 19:26:29.288] [2ffdc8cb] [1.031s] INFO in eda_mcp_server: Base path: Not set
[2025-08-05 19:26:29.289] [2ffdc8cb] [1.031s] INFO in eda_mcp_server: Available tools: 1 tools registered
[2025-08-05 19:26:29.289] [2ffdc8cb] [1.031s] INFO in eda_mcp_server: Python version: 3.12.5 (tags/v3.12.5:ff3bc82, Aug  6 2024, 20:45:27) [MSC v.1940 64 bit (AMD64)]
[2025-08-05 19:26:29.289] [2ffdc8cb] [1.032s] INFO in eda_mcp_server: Working directory: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp
[2025-08-05 19:26:29.290] [2ffdc8cb] [1.032s] INFO in eda_mcp_server: AZURE_OPENAI_ENDPOINT: https://sivanithish-...
[2025-08-05 19:26:29.290] [2ffdc8cb] [1.032s] INFO in eda_mcp_server: AZURE_OPENAI_DEPLOYMENT_NAME: gpt-4o...
[2025-08-05 19:26:29.290] [2ffdc8cb] [1.033s] INFO in eda_mcp_server: EDA_MCP_BASE_PATH: Not set
[2025-08-05 19:26:29.291] [2ffdc8cb] [1.033s] INFO in eda_mcp_server: EDA MCP Server initialization complete
[2025-08-05 19:26:29.291] [2ffdc8cb] [1.033s] INFO in eda_mcp_server: ============================================================
