[2025-08-05 19:11:21.275] [057b33ff] [0.000s] INFO in eda_mcp_server: EDA MCP Server session started - ID: 057b33ff
[2025-08-05 19:11:21.275] [057b33ff] [0.001s] INFO in eda_mcp_server: Log directory: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp\logs\session_057b33ff_20250805_191117
[2025-08-05 19:11:21.276] [057b33ff] [0.001s] INFO in eda_mcp_server: Initializing EDA MCP Server
[2025-08-05 19:11:21.276] [057b33ff] [0.001s] INFO in eda_mcp_server: Configuration loaded - endpoint: https://sivanithish-test.openai.azure.com/, deployment: gpt-4o
[2025-08-05 19:11:21.276] [057b33ff] [0.001s] INFO in eda_mcp_server: Jina API key configured: jina_704d29e58ed547c...
[2025-08-05 19:11:21.276] [057b33ff] [0.001s] INFO in eda_mcp_server: Configuring Azure OpenAI client
[2025-08-05 19:11:21.886] [057b33ff] [0.612s] INFO in eda_mcp_server: Azure OpenAI configured with model: gpt-4o
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Starting EDA MCP server...
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Version: 0.1.0
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Model: gpt-4o
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Base path: Not set
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Available tools: 1 tools registered
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Python version: 3.13.2 (main, Mar 11 2025, 17:20:07) [MSC v.1943 64 bit (AMD64)]
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Working directory: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp
[2025-08-05 19:11:21.930] [057b33ff] [0.656s] INFO in eda_mcp_server: AZURE_OPENAI_ENDPOINT: https://sivanithish-...
[2025-08-05 19:11:21.930] [057b33ff] [0.656s] INFO in eda_mcp_server: AZURE_OPENAI_DEPLOYMENT_NAME: gpt-4o...
[2025-08-05 19:11:21.930] [057b33ff] [0.656s] INFO in eda_mcp_server: EDA_MCP_BASE_PATH: Not set
[2025-08-05 19:11:21.930] [057b33ff] [0.656s] INFO in eda_mcp_server: EDA MCP Server initialization complete
[2025-08-05 19:11:21.931] [057b33ff] [0.656s] INFO in eda_mcp_server: ============================================================
[2025-08-05 19:12:43.941] [057b33ff] [82.666s] INFO in eda_mcp_server: Starting tool: summarize_dataset
[2025-08-05 19:12:43.946] [057b33ff] [82.671s] INFO in eda_mcp_server: LLM call in summarize_dataset: gpt-4o, tokens: 2000
[2025-08-05 19:12:44.075] [057b33ff] [82.800s] INFO in eda_mcp_server: Tool completed successfully: summarize_dataset
[2025-08-05 19:12:47.949] [057b33ff] [86.674s] INFO in eda_mcp_server: Starting tool: perform_eda
