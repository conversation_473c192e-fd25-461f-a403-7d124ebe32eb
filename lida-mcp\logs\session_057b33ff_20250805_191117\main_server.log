[2025-08-05 19:11:21.275] [057b33ff] [0.000s] INFO in eda_mcp_server: EDA MCP Server session started - ID: 057b33ff
[2025-08-05 19:11:21.275] [057b33ff] [0.001s] INFO in eda_mcp_server: Log directory: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp\logs\session_057b33ff_20250805_191117
[2025-08-05 19:11:21.276] [057b33ff] [0.001s] INFO in eda_mcp_server: Initializing EDA MCP Server
[2025-08-05 19:11:21.276] [057b33ff] [0.001s] INFO in eda_mcp_server: Configuration loaded - endpoint: https://sivanithish-test.openai.azure.com/, deployment: gpt-4o
[2025-08-05 19:11:21.276] [057b33ff] [0.001s] INFO in eda_mcp_server: Jina API key configured: jina_704d29e58ed547c...
[2025-08-05 19:11:21.276] [057b33ff] [0.001s] INFO in eda_mcp_server: Configuring Azure OpenAI client
[2025-08-05 19:11:21.886] [057b33ff] [0.612s] INFO in eda_mcp_server: Azure OpenAI configured with model: gpt-4o
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Starting EDA MCP server...
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Version: 0.1.0
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Model: gpt-4o
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Base path: Not set
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Available tools: 1 tools registered
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Python version: 3.13.2 (main, Mar 11 2025, 17:20:07) [MSC v.1943 64 bit (AMD64)]
[2025-08-05 19:11:21.930] [057b33ff] [0.655s] INFO in eda_mcp_server: Working directory: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp
[2025-08-05 19:11:21.930] [057b33ff] [0.656s] INFO in eda_mcp_server: AZURE_OPENAI_ENDPOINT: https://sivanithish-...
[2025-08-05 19:11:21.930] [057b33ff] [0.656s] INFO in eda_mcp_server: AZURE_OPENAI_DEPLOYMENT_NAME: gpt-4o...
[2025-08-05 19:11:21.930] [057b33ff] [0.656s] INFO in eda_mcp_server: EDA_MCP_BASE_PATH: Not set
[2025-08-05 19:11:21.930] [057b33ff] [0.656s] INFO in eda_mcp_server: EDA MCP Server initialization complete
[2025-08-05 19:11:21.931] [057b33ff] [0.656s] INFO in eda_mcp_server: ============================================================
[2025-08-05 19:12:43.941] [057b33ff] [82.666s] INFO in eda_mcp_server: Starting tool: summarize_dataset
[2025-08-05 19:12:43.946] [057b33ff] [82.671s] INFO in eda_mcp_server: LLM call in summarize_dataset: gpt-4o, tokens: 2000
[2025-08-05 19:12:44.075] [057b33ff] [82.800s] INFO in eda_mcp_server: Tool completed successfully: summarize_dataset
[2025-08-05 19:12:47.949] [057b33ff] [86.674s] INFO in eda_mcp_server: Starting tool: perform_eda
[2025-08-05 19:17:08.258] [057b33ff] [346.983s] INFO in eda_mcp_server: Tool completed successfully: perform_eda
[2025-08-05 19:17:08.327] [057b33ff] [347.052s] INFO in eda_mcp_server: Starting tool: perform_eda
[2025-08-05 19:17:08.434] [057b33ff] [347.160s] INFO in eda_mcp_server: Tool completed successfully: perform_eda
[2025-08-05 19:17:12.895] [057b33ff] [351.620s] INFO in eda_mcp_server: Starting tool: perform_eda
[2025-08-05 19:17:26.739] [057b33ff] [365.465s] INFO in eda_mcp_server: Tool completed successfully: perform_eda
[2025-08-05 19:18:43.959] [057b33ff] [442.684s] INFO in eda_mcp_server: Starting tool: perform_eda
[2025-08-05 19:18:51.823] [057b33ff] [450.548s] INFO in eda_mcp_server: Tool completed successfully: perform_eda
[2025-08-05 19:18:58.120] [057b33ff] [456.845s] INFO in eda_mcp_server: Starting tool: perform_eda
[2025-08-05 19:19:03.478] [057b33ff] [462.203s] INFO in eda_mcp_server: Tool completed successfully: perform_eda
[2025-08-05 19:19:10.473] [057b33ff] [469.199s] INFO in eda_mcp_server: Starting tool: perform_eda
[2025-08-05 19:19:17.019] [057b33ff] [475.744s] INFO in eda_mcp_server: Tool completed successfully: perform_eda
[2025-08-05 19:27:14.999] [057b33ff] [953.741s] INFO in eda_mcp_server: EDA MCP Server shutting down...
[2025-08-05 19:27:15.030] [057b33ff] [953.755s] INFO in eda_mcp_server: ============================================================
[2025-08-05 19:27:15.030] [057b33ff] [953.755s] INFO in eda_mcp_server: SESSION SUMMARY
[2025-08-05 19:27:15.030] [057b33ff] [953.756s] INFO in eda_mcp_server: ============================================================
[2025-08-05 19:27:15.031] [057b33ff] [953.756s] INFO in eda_mcp_server: Session ID: 057b33ff
[2025-08-05 19:27:15.032] [057b33ff] [953.757s] INFO in eda_mcp_server: Duration: 0:15:57.515971
[2025-08-05 19:27:15.033] [057b33ff] [953.758s] INFO in eda_mcp_server: Tools used: 5
[2025-08-05 19:27:15.033] [057b33ff] [953.758s] INFO in eda_mcp_server: Tool list: summarize_dataset, create_fresh_lida_manager, perform_eda, EDAEngine, CustomAnalysisModule
[2025-08-05 19:27:15.033] [057b33ff] [953.759s] INFO in eda_mcp_server: Log directory: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp\logs\session_057b33ff_20250805_191117
[2025-08-05 19:27:15.034] [057b33ff] [953.759s] INFO in eda_mcp_server: ============================================================
[2025-08-05 19:27:15.068] [057b33ff] [953.799s] INFO in eda_mcp_server: ============================================================
[2025-08-05 19:27:15.074] [057b33ff] [953.800s] INFO in eda_mcp_server: SESSION SUMMARY
[2025-08-05 19:27:15.075] [057b33ff] [953.800s] INFO in eda_mcp_server: ============================================================
[2025-08-05 19:27:15.075] [057b33ff] [953.800s] INFO in eda_mcp_server: Session ID: 057b33ff
[2025-08-05 19:27:15.075] [057b33ff] [953.800s] INFO in eda_mcp_server: Duration: 0:15:57.555164
[2025-08-05 19:27:15.075] [057b33ff] [953.800s] INFO in eda_mcp_server: Tools used: 5
[2025-08-05 19:27:15.075] [057b33ff] [953.800s] INFO in eda_mcp_server: Tool list: summarize_dataset, create_fresh_lida_manager, perform_eda, EDAEngine, CustomAnalysisModule
[2025-08-05 19:27:15.075] [057b33ff] [953.801s] INFO in eda_mcp_server: Log directory: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp\logs\session_057b33ff_20250805_191117
[2025-08-05 19:27:15.076] [057b33ff] [953.801s] INFO in eda_mcp_server: ============================================================
