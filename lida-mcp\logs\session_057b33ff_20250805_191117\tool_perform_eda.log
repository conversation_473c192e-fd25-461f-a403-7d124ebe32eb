[2025-08-05 19:12:47.948] [057b33ff] [0.001s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:12:47.949] [057b33ff] [0.001s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "advanced",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": null,
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 19:12:47.949] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:12:47.949] [057b33ff] [0.001s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Analysis type: advanced, depth: comprehensive
[2025-08-05 19:12:47.992] [057b33ff] [0.044s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA engine initialized with modules
[2025-08-05 19:17:08.254] [057b33ff] [260.306s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA analysis completed in 260.26 seconds
[2025-08-05 19:17:08.257] [057b33ff] [260.309s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 260.308s
[2025-08-05 19:17:08.257] [057b33ff] [260.310s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:17:08.258] [057b33ff] [260.310s] INFO in eda_mcp_tool_perform_eda: Result length: 1758 characters
[2025-08-05 19:17:08.326] [057b33ff] [260.379s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:17:08.327] [057b33ff] [260.379s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "basic",
  "analysis_depth": "standard",
  "target_columns": null,
  "analysis_goals": null,
  "include_visualizations": false,
  "max_execution_time": 300,
  "sample_size": null
}
[2025-08-05 19:17:08.327] [057b33ff] [260.379s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:17:08.327] [057b33ff] [260.379s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Analysis type: basic, depth: standard
[2025-08-05 19:17:08.327] [057b33ff] [260.380s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA engine initialized with modules
[2025-08-05 19:17:08.434] [057b33ff] [260.486s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA analysis completed in 0.11 seconds
[2025-08-05 19:17:08.434] [057b33ff] [260.487s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 0.107s
[2025-08-05 19:17:08.434] [057b33ff] [260.487s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:17:08.434] [057b33ff] [260.487s] INFO in eda_mcp_tool_perform_eda: Result length: 1685 characters
[2025-08-05 19:17:12.894] [057b33ff] [264.947s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:17:12.895] [057b33ff] [264.947s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Analyze forecast accuracy and performance patterns across different profit centers and carlines"
  ],
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 19:17:12.895] [057b33ff] [264.947s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:17:12.895] [057b33ff] [264.947s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Analysis type: custom, depth: comprehensive
[2025-08-05 19:17:12.896] [057b33ff] [264.948s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA engine initialized with modules
[2025-08-05 19:17:26.739] [057b33ff] [278.791s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA analysis completed in 13.84 seconds
[2025-08-05 19:17:26.739] [057b33ff] [278.791s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 13.844s
[2025-08-05 19:17:26.739] [057b33ff] [278.791s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:17:26.739] [057b33ff] [278.792s] INFO in eda_mcp_tool_perform_eda: Result length: 4059 characters
[2025-08-05 19:18:43.958] [057b33ff] [356.010s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:18:43.959] [057b33ff] [356.011s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Perform comprehensive time series analysis including trend analysis, seasonality detection, forecasting accuracy over time, and temporal patterns in order quantities and forecasts"
  ],
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 19:18:43.959] [057b33ff] [356.012s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:18:43.959] [057b33ff] [356.012s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Analysis type: custom, depth: comprehensive
[2025-08-05 19:18:43.960] [057b33ff] [356.013s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA engine initialized with modules
[2025-08-05 19:18:51.821] [057b33ff] [363.874s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA analysis completed in 7.86 seconds
[2025-08-05 19:18:51.823] [057b33ff] [363.875s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 7.863s
[2025-08-05 19:18:51.823] [057b33ff] [363.875s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:18:51.823] [057b33ff] [363.875s] INFO in eda_mcp_tool_perform_eda: Result length: 4313 characters
[2025-08-05 19:18:58.120] [057b33ff] [370.172s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:18:58.120] [057b33ff] [370.172s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Analyze reference_date time series patterns, calculate forecast accuracy trends over time, detect seasonality in monthly data, and identify temporal correlations between different forecast types and actual orders"
  ],
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 19:18:58.120] [057b33ff] [370.172s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:18:58.120] [057b33ff] [370.172s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Analysis type: custom, depth: comprehensive
[2025-08-05 19:18:58.120] [057b33ff] [370.173s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA engine initialized with modules
[2025-08-05 19:19:03.475] [057b33ff] [375.528s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA analysis completed in 5.35 seconds
[2025-08-05 19:19:03.478] [057b33ff] [375.530s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 5.358s
[2025-08-05 19:19:03.478] [057b33ff] [375.530s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:19:03.478] [057b33ff] [375.530s] INFO in eda_mcp_tool_perform_eda: Result length: 3743 characters
[2025-08-05 19:19:10.473] [057b33ff] [382.525s] INFO in eda_mcp_tool_perform_eda: === TOOL START: perform_eda ===
[2025-08-05 19:19:10.473] [057b33ff] [382.525s] INFO in eda_mcp_tool_perform_eda: Parameters: {
  "file_path": "C:\\Users\\<USER>\\Downloads\\swtf_v1.csv",
  "analysis_type": "custom",
  "analysis_depth": "comprehensive",
  "target_columns": null,
  "analysis_goals": [
    "Create detailed time series analysis with reference_date trends, calculate forecast bias and accuracy metrics over time periods, identify weekly/monthly patterns, and perform statistical tests for trend significance"
  ],
  "include_visualizations": false,
  "max_execution_time": 600,
  "sample_size": null
}
[2025-08-05 19:19:10.474] [057b33ff] [382.526s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Starting EDA analysis for: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:19:10.474] [057b33ff] [382.526s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Analysis type: custom, depth: comprehensive
[2025-08-05 19:19:10.474] [057b33ff] [382.526s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA engine initialized with modules
[2025-08-05 19:19:17.014] [057b33ff] [389.067s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: EDA analysis completed in 6.54 seconds
[2025-08-05 19:19:17.019] [057b33ff] [389.071s] DEBUG in eda_mcp_tool_perform_eda - log_tool_step(): STEP: Execution completed in 6.545s
[2025-08-05 19:19:17.019] [057b33ff] [389.071s] INFO in eda_mcp_tool_perform_eda: === TOOL SUCCESS: perform_eda ===
[2025-08-05 19:19:17.019] [057b33ff] [389.071s] INFO in eda_mcp_tool_perform_eda: Result length: 3775 characters
