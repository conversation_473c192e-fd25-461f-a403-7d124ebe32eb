[2025-08-05 19:29:22.149] [aa95cdb4] [0.001s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 19:29:22.149] [aa95cdb4] [0.002s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 19:29:22.150] [aa95cdb4] [0.002s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Request details - File: C:\Users\<USER>\Downloads\swtf_v1.csv, Goals: ['Perform comprehensive time series analysis including trend analysis, seasonality detection, forecasting accuracy over time, and temporal patterns in order quantities and forecasts']
[2025-08-05 19:29:22.150] [aa95cdb4] [0.003s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 1/6: Loading and validating data
[2025-08-05 19:29:22.151] [aa95cdb4] [0.003s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:29:22.152] [aa95cdb4] [0.004s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: File exists, size: 0.02 MB
[2025-08-05 19:29:22.268] [aa95cdb4] [0.120s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:29:22.268] [aa95cdb4] [0.120s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:29:22.268] [aa95cdb4] [0.121s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ Data loaded successfully: 174 rows, 19 columns
[2025-08-05 19:29:22.269] [aa95cdb4] [0.121s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Columns: ['reference_date', 'pc', 'profit_center_program', 'carline', 'month', 'lag_month', 'actual_order_quantity', 'forecast_percentile', 'sw_forecast_quantity', 'customer_forecast_quantity', 'prorated_derate', 'prorated_edi', 'edi', 'derate', 'total_weeks', 'in_scope_weeks', 'forecast_count', 'min_target_date', 'max_target_date']
[2025-08-05 19:29:22.285] [aa95cdb4] [0.137s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Data types: {'reference_date': dtype('O'), 'pc': dtype('O'), 'profit_center_program': dtype('O'), 'carline': dtype('O'), 'month': dtype('O'), 'lag_month': dtype('float64'), 'actual_order_quantity': dtype('float64'), 'forecast_percentile': dtype('O'), 'sw_forecast_quantity': dtype('float64'), 'customer_forecast_quantity': dtype('float64'), 'prorated_derate': dtype('int64'), 'prorated_edi': dtype('int64'), 'edi': dtype('int64'), 'derate': dtype('int64'), 'total_weeks': dtype('int64'), 'in_scope_weeks': dtype('int64'), 'forecast_count': dtype('float64'), 'min_target_date': dtype('O'), 'max_target_date': dtype('O')}
[2025-08-05 19:29:22.286] [aa95cdb4] [0.138s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 2/6: Applying sampling if needed
[2025-08-05 19:29:22.286] [aa95cdb4] [0.138s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 3/6: Selecting analyses to perform
[2025-08-05 19:29:22.286] [aa95cdb4] [0.138s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 19:29:22.286] [aa95cdb4] [0.138s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 4/6: Executing analyses
[2025-08-05 19:29:22.286] [aa95cdb4] [0.138s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing 1 analysis modules: ['custom']
[2025-08-05 19:29:22.286] [aa95cdb4] [0.138s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: [1/1] Starting analysis: custom
[2025-08-05 19:29:22.286] [aa95cdb4] [0.138s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Options for custom: {'target_columns': None, 'analysis_goals': ['Perform comprehensive time series analysis including trend analysis, seasonality detection, forecasting accuracy over time, and temporal patterns in order quantities and forecasts'], 'analysis_depth': 'comprehensive'}
[2025-08-05 19:29:47.331] [aa95cdb4] [25.183s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ [1/1] Completed analysis: custom in 25.04s
[2025-08-05 19:29:47.331] [aa95cdb4] [25.183s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result keys for custom: ['custom_analysis']
[2025-08-05 19:29:47.331] [aa95cdb4] [25.184s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Analysis execution completed. Successful modules: 1
[2025-08-05 19:29:47.332] [aa95cdb4] [25.184s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 5/6: Generating visualizations
[2025-08-05 19:29:47.333] [aa95cdb4] [25.185s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 6/6: Generating recommendations
[2025-08-05 19:29:47.333] [aa95cdb4] [25.185s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Creating final result
[2025-08-05 19:29:47.341] [aa95cdb4] [25.193s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ EDA analysis completed successfully in 25.19 seconds
[2025-08-05 19:29:47.341] [aa95cdb4] [25.193s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result contains 1 analysis modules
[2025-08-05 19:29:52.390] [aa95cdb4] [30.242s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 19:29:52.390] [aa95cdb4] [30.242s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 19:29:52.390] [aa95cdb4] [30.242s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Request details - File: C:\Users\<USER>\Downloads\swtf_v1.csv, Goals: ['Analyze temporal trends in actual_order_quantity and forecast accuracy, calculate monthly aggregations, identify seasonal patterns, and compare forecast performance across different time periods']
[2025-08-05 19:29:52.390] [aa95cdb4] [30.242s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 1/6: Loading and validating data
[2025-08-05 19:29:52.390] [aa95cdb4] [30.242s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:29:52.391] [aa95cdb4] [30.243s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: File exists, size: 0.02 MB
[2025-08-05 19:29:52.394] [aa95cdb4] [30.246s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:29:52.394] [aa95cdb4] [30.247s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:29:52.395] [aa95cdb4] [30.247s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ Data loaded successfully: 174 rows, 19 columns
[2025-08-05 19:29:52.395] [aa95cdb4] [30.247s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Columns: ['reference_date', 'pc', 'profit_center_program', 'carline', 'month', 'lag_month', 'actual_order_quantity', 'forecast_percentile', 'sw_forecast_quantity', 'customer_forecast_quantity', 'prorated_derate', 'prorated_edi', 'edi', 'derate', 'total_weeks', 'in_scope_weeks', 'forecast_count', 'min_target_date', 'max_target_date']
[2025-08-05 19:29:52.395] [aa95cdb4] [30.247s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Data types: {'reference_date': dtype('O'), 'pc': dtype('O'), 'profit_center_program': dtype('O'), 'carline': dtype('O'), 'month': dtype('O'), 'lag_month': dtype('float64'), 'actual_order_quantity': dtype('float64'), 'forecast_percentile': dtype('O'), 'sw_forecast_quantity': dtype('float64'), 'customer_forecast_quantity': dtype('float64'), 'prorated_derate': dtype('int64'), 'prorated_edi': dtype('int64'), 'edi': dtype('int64'), 'derate': dtype('int64'), 'total_weeks': dtype('int64'), 'in_scope_weeks': dtype('int64'), 'forecast_count': dtype('float64'), 'min_target_date': dtype('O'), 'max_target_date': dtype('O')}
[2025-08-05 19:29:52.395] [aa95cdb4] [30.248s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 2/6: Applying sampling if needed
[2025-08-05 19:29:52.395] [aa95cdb4] [30.248s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 3/6: Selecting analyses to perform
[2025-08-05 19:29:52.396] [aa95cdb4] [30.248s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 19:29:52.396] [aa95cdb4] [30.248s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 4/6: Executing analyses
[2025-08-05 19:29:52.396] [aa95cdb4] [30.248s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing 1 analysis modules: ['custom']
[2025-08-05 19:29:52.396] [aa95cdb4] [30.248s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: [1/1] Starting analysis: custom
[2025-08-05 19:29:52.396] [aa95cdb4] [30.248s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Options for custom: {'target_columns': None, 'analysis_goals': ['Analyze temporal trends in actual_order_quantity and forecast accuracy, calculate monthly aggregations, identify seasonal patterns, and compare forecast performance across different time periods'], 'analysis_depth': 'comprehensive'}
[2025-08-05 19:33:52.125] [aa95cdb4] [269.977s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ [1/1] Completed analysis: custom in 239.73s
[2025-08-05 19:33:52.125] [aa95cdb4] [269.977s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result keys for custom: ['custom_analysis']
[2025-08-05 19:33:52.125] [aa95cdb4] [269.977s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Analysis execution completed. Successful modules: 1
[2025-08-05 19:33:52.125] [aa95cdb4] [269.978s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 5/6: Generating visualizations
[2025-08-05 19:33:52.126] [aa95cdb4] [269.978s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 6/6: Generating recommendations
[2025-08-05 19:33:52.126] [aa95cdb4] [269.978s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Creating final result
[2025-08-05 19:33:52.130] [aa95cdb4] [269.982s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ EDA analysis completed successfully in 239.74 seconds
[2025-08-05 19:33:52.130] [aa95cdb4] [269.982s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result contains 1 analysis modules
[2025-08-05 19:33:52.186] [aa95cdb4] [270.038s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 19:33:52.186] [aa95cdb4] [270.039s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 19:33:52.187] [aa95cdb4] [270.039s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Request details - File: C:\Users\<USER>\Downloads\swtf_v1.csv, Goals: ['Perform detailed time series analysis including decomposition of trends and seasonality, autocorrelation analysis, forecast error analysis over time, moving averages, and time-based performance metrics across different time periods']
[2025-08-05 19:33:52.187] [aa95cdb4] [270.039s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 1/6: Loading and validating data
[2025-08-05 19:33:52.187] [aa95cdb4] [270.039s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:33:52.187] [aa95cdb4] [270.039s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: File exists, size: 0.02 MB
[2025-08-05 19:33:52.194] [aa95cdb4] [270.046s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:33:52.194] [aa95cdb4] [270.046s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:33:52.194] [aa95cdb4] [270.046s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ Data loaded successfully: 174 rows, 19 columns
[2025-08-05 19:33:52.194] [aa95cdb4] [270.046s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Columns: ['reference_date', 'pc', 'profit_center_program', 'carline', 'month', 'lag_month', 'actual_order_quantity', 'forecast_percentile', 'sw_forecast_quantity', 'customer_forecast_quantity', 'prorated_derate', 'prorated_edi', 'edi', 'derate', 'total_weeks', 'in_scope_weeks', 'forecast_count', 'min_target_date', 'max_target_date']
[2025-08-05 19:33:52.195] [aa95cdb4] [270.047s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Data types: {'reference_date': dtype('O'), 'pc': dtype('O'), 'profit_center_program': dtype('O'), 'carline': dtype('O'), 'month': dtype('O'), 'lag_month': dtype('float64'), 'actual_order_quantity': dtype('float64'), 'forecast_percentile': dtype('O'), 'sw_forecast_quantity': dtype('float64'), 'customer_forecast_quantity': dtype('float64'), 'prorated_derate': dtype('int64'), 'prorated_edi': dtype('int64'), 'edi': dtype('int64'), 'derate': dtype('int64'), 'total_weeks': dtype('int64'), 'in_scope_weeks': dtype('int64'), 'forecast_count': dtype('float64'), 'min_target_date': dtype('O'), 'max_target_date': dtype('O')}
[2025-08-05 19:33:52.195] [aa95cdb4] [270.047s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 2/6: Applying sampling if needed
[2025-08-05 19:33:52.195] [aa95cdb4] [270.047s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 3/6: Selecting analyses to perform
[2025-08-05 19:33:52.195] [aa95cdb4] [270.047s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 19:33:52.195] [aa95cdb4] [270.047s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 4/6: Executing analyses
[2025-08-05 19:33:52.195] [aa95cdb4] [270.047s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing 1 analysis modules: ['custom']
[2025-08-05 19:33:52.195] [aa95cdb4] [270.047s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: [1/1] Starting analysis: custom
[2025-08-05 19:33:52.195] [aa95cdb4] [270.047s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Options for custom: {'target_columns': None, 'analysis_goals': ['Perform detailed time series analysis including decomposition of trends and seasonality, autocorrelation analysis, forecast error analysis over time, moving averages, and time-based performance metrics across different time periods'], 'analysis_depth': 'comprehensive'}
[2025-08-05 19:34:23.540] [aa95cdb4] [301.392s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ [1/1] Completed analysis: custom in 31.35s
[2025-08-05 19:34:23.540] [aa95cdb4] [301.392s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result keys for custom: ['custom_analysis']
[2025-08-05 19:34:23.541] [aa95cdb4] [301.393s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Analysis execution completed. Successful modules: 1
[2025-08-05 19:34:23.541] [aa95cdb4] [301.393s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 5/6: Generating visualizations
[2025-08-05 19:34:23.541] [aa95cdb4] [301.393s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 6/6: Generating recommendations
[2025-08-05 19:34:23.541] [aa95cdb4] [301.393s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Creating final result
[2025-08-05 19:34:23.546] [aa95cdb4] [301.398s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ EDA analysis completed successfully in 31.36 seconds
[2025-08-05 19:34:23.550] [aa95cdb4] [301.402s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result contains 1 analysis modules
[2025-08-05 19:34:23.578] [aa95cdb4] [301.430s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 19:34:23.578] [aa95cdb4] [301.431s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 19:34:23.579] [aa95cdb4] [301.431s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Request details - File: C:\Users\<USER>\Downloads\swtf_v1.csv, Goals: ['Focus on time series patterns in reference_date and month columns, analyze order quantity trends over time, and calculate basic forecast accuracy metrics']
[2025-08-05 19:34:23.579] [aa95cdb4] [301.431s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 1/6: Loading and validating data
[2025-08-05 19:34:23.579] [aa95cdb4] [301.431s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 19:34:23.579] [aa95cdb4] [301.431s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: File exists, size: 0.02 MB
[2025-08-05 19:34:23.584] [aa95cdb4] [301.436s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 19:34:23.585] [aa95cdb4] [301.437s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 19:34:23.585] [aa95cdb4] [301.437s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ Data loaded successfully: 174 rows, 19 columns
[2025-08-05 19:34:23.585] [aa95cdb4] [301.437s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Columns: ['reference_date', 'pc', 'profit_center_program', 'carline', 'month', 'lag_month', 'actual_order_quantity', 'forecast_percentile', 'sw_forecast_quantity', 'customer_forecast_quantity', 'prorated_derate', 'prorated_edi', 'edi', 'derate', 'total_weeks', 'in_scope_weeks', 'forecast_count', 'min_target_date', 'max_target_date']
[2025-08-05 19:34:23.585] [aa95cdb4] [301.438s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Data types: {'reference_date': dtype('O'), 'pc': dtype('O'), 'profit_center_program': dtype('O'), 'carline': dtype('O'), 'month': dtype('O'), 'lag_month': dtype('float64'), 'actual_order_quantity': dtype('float64'), 'forecast_percentile': dtype('O'), 'sw_forecast_quantity': dtype('float64'), 'customer_forecast_quantity': dtype('float64'), 'prorated_derate': dtype('int64'), 'prorated_edi': dtype('int64'), 'edi': dtype('int64'), 'derate': dtype('int64'), 'total_weeks': dtype('int64'), 'in_scope_weeks': dtype('int64'), 'forecast_count': dtype('float64'), 'min_target_date': dtype('O'), 'max_target_date': dtype('O')}
[2025-08-05 19:34:23.586] [aa95cdb4] [301.438s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 2/6: Applying sampling if needed
[2025-08-05 19:34:23.586] [aa95cdb4] [301.438s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 3/6: Selecting analyses to perform
[2025-08-05 19:34:23.586] [aa95cdb4] [301.438s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 19:34:23.586] [aa95cdb4] [301.438s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 4/6: Executing analyses
[2025-08-05 19:34:23.586] [aa95cdb4] [301.438s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing 1 analysis modules: ['custom']
[2025-08-05 19:34:23.586] [aa95cdb4] [301.438s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: [1/1] Starting analysis: custom
[2025-08-05 19:34:23.586] [aa95cdb4] [301.438s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Options for custom: {'target_columns': None, 'analysis_goals': ['Focus on time series patterns in reference_date and month columns, analyze order quantity trends over time, and calculate basic forecast accuracy metrics'], 'analysis_depth': 'standard'}
[2025-08-05 19:34:36.448] [aa95cdb4] [314.300s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ [1/1] Completed analysis: custom in 12.86s
[2025-08-05 19:34:36.448] [aa95cdb4] [314.300s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result keys for custom: ['custom_analysis']
[2025-08-05 19:34:36.448] [aa95cdb4] [314.300s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Analysis execution completed. Successful modules: 1
[2025-08-05 19:34:36.448] [aa95cdb4] [314.301s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 5/6: Generating visualizations
[2025-08-05 19:34:36.449] [aa95cdb4] [314.301s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 6/6: Generating recommendations
[2025-08-05 19:34:36.449] [aa95cdb4] [314.301s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Creating final result
[2025-08-05 19:34:36.454] [aa95cdb4] [314.306s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ EDA analysis completed successfully in 12.88 seconds
[2025-08-05 19:34:36.455] [aa95cdb4] [314.307s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result contains 1 analysis modules
[2025-08-05 20:07:58.826] [aa95cdb4] [2316.679s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 20:07:58.829] [aa95cdb4] [2316.681s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 20:07:58.829] [aa95cdb4] [2316.681s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Request details - File: C:\Users\<USER>\Downloads\swtf_v1.csv, Goals: ['Perform advanced time series modeling including decomposition analysis, autocorrelation analysis, forecast model validation, residual analysis, and future trend projections based on the identified growth patterns']
[2025-08-05 20:07:58.831] [aa95cdb4] [2316.683s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 1/6: Loading and validating data
[2025-08-05 20:07:58.831] [aa95cdb4] [2316.683s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 20:07:58.836] [aa95cdb4] [2316.688s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: File exists, size: 0.02 MB
[2025-08-05 20:07:58.949] [aa95cdb4] [2316.801s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 20:07:58.949] [aa95cdb4] [2316.801s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 20:07:58.949] [aa95cdb4] [2316.802s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ Data loaded successfully: 174 rows, 19 columns
[2025-08-05 20:07:58.950] [aa95cdb4] [2316.803s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Columns: ['reference_date', 'pc', 'profit_center_program', 'carline', 'month', 'lag_month', 'actual_order_quantity', 'forecast_percentile', 'sw_forecast_quantity', 'customer_forecast_quantity', 'prorated_derate', 'prorated_edi', 'edi', 'derate', 'total_weeks', 'in_scope_weeks', 'forecast_count', 'min_target_date', 'max_target_date']
[2025-08-05 20:07:58.963] [aa95cdb4] [2316.815s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Data types: {'reference_date': dtype('O'), 'pc': dtype('O'), 'profit_center_program': dtype('O'), 'carline': dtype('O'), 'month': dtype('O'), 'lag_month': dtype('float64'), 'actual_order_quantity': dtype('float64'), 'forecast_percentile': dtype('O'), 'sw_forecast_quantity': dtype('float64'), 'customer_forecast_quantity': dtype('float64'), 'prorated_derate': dtype('int64'), 'prorated_edi': dtype('int64'), 'edi': dtype('int64'), 'derate': dtype('int64'), 'total_weeks': dtype('int64'), 'in_scope_weeks': dtype('int64'), 'forecast_count': dtype('float64'), 'min_target_date': dtype('O'), 'max_target_date': dtype('O')}
[2025-08-05 20:07:58.964] [aa95cdb4] [2316.816s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 2/6: Applying sampling if needed
[2025-08-05 20:07:58.964] [aa95cdb4] [2316.816s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 3/6: Selecting analyses to perform
[2025-08-05 20:07:58.966] [aa95cdb4] [2316.818s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 20:07:58.966] [aa95cdb4] [2316.818s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 4/6: Executing analyses
[2025-08-05 20:07:58.966] [aa95cdb4] [2316.818s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing 1 analysis modules: ['custom']
[2025-08-05 20:07:58.966] [aa95cdb4] [2316.818s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: [1/1] Starting analysis: custom
[2025-08-05 20:07:58.966] [aa95cdb4] [2316.818s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Options for custom: {'target_columns': None, 'analysis_goals': ['Perform advanced time series modeling including decomposition analysis, autocorrelation analysis, forecast model validation, residual analysis, and future trend projections based on the identified growth patterns'], 'analysis_depth': 'comprehensive'}
[2025-08-05 20:08:20.084] [aa95cdb4] [2337.936s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ [1/1] Completed analysis: custom in 21.12s
[2025-08-05 20:08:20.084] [aa95cdb4] [2337.936s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result keys for custom: ['custom_analysis']
[2025-08-05 20:08:20.084] [aa95cdb4] [2337.936s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Analysis execution completed. Successful modules: 1
[2025-08-05 20:08:20.084] [aa95cdb4] [2337.936s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 5/6: Generating visualizations
[2025-08-05 20:08:20.084] [aa95cdb4] [2337.936s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 6/6: Generating recommendations
[2025-08-05 20:08:20.084] [aa95cdb4] [2337.936s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Creating final result
[2025-08-05 20:08:20.086] [aa95cdb4] [2337.938s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ EDA analysis completed successfully in 21.26 seconds
[2025-08-05 20:08:20.087] [aa95cdb4] [2337.939s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result contains 1 analysis modules
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.849s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Registered analysis module: custom
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.849s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Starting EDA analysis: custom
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.849s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Request details - File: C:\Users\<USER>\Downloads\swtf_v1.csv, Goals: ['Perform forecast model optimization analysis including bias detection, forecast horizon analysis, model performance by profit center and carline, and statistical model comparison between SW and customer forecasts']
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.849s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 1/6: Loading and validating data
[2025-08-05 20:08:25.997] [aa95cdb4] [2343.849s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Loading data from: C:\Users\<USER>\Downloads\swtf_v1.csv
[2025-08-05 20:08:25.998] [aa95cdb4] [2343.850s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: File exists, size: 0.02 MB
[2025-08-05 20:08:26.003] [aa95cdb4] [2343.855s] INFO in eda_mcp_tool_EDAEngine: Data operation: data_loaded
[2025-08-05 20:08:26.004] [aa95cdb4] [2343.856s] INFO in eda_mcp_tool_EDAEngine: Data info: {
  "rows": 174,
  "columns": 19,
  "file_size_mb": 0.023772239685058594
}
[2025-08-05 20:08:26.004] [aa95cdb4] [2343.856s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ Data loaded successfully: 174 rows, 19 columns
[2025-08-05 20:08:26.004] [aa95cdb4] [2343.856s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Columns: ['reference_date', 'pc', 'profit_center_program', 'carline', 'month', 'lag_month', 'actual_order_quantity', 'forecast_percentile', 'sw_forecast_quantity', 'customer_forecast_quantity', 'prorated_derate', 'prorated_edi', 'edi', 'derate', 'total_weeks', 'in_scope_weeks', 'forecast_count', 'min_target_date', 'max_target_date']
[2025-08-05 20:08:26.004] [aa95cdb4] [2343.856s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Data types: {'reference_date': dtype('O'), 'pc': dtype('O'), 'profit_center_program': dtype('O'), 'carline': dtype('O'), 'month': dtype('O'), 'lag_month': dtype('float64'), 'actual_order_quantity': dtype('float64'), 'forecast_percentile': dtype('O'), 'sw_forecast_quantity': dtype('float64'), 'customer_forecast_quantity': dtype('float64'), 'prorated_derate': dtype('int64'), 'prorated_edi': dtype('int64'), 'edi': dtype('int64'), 'derate': dtype('int64'), 'total_weeks': dtype('int64'), 'in_scope_weeks': dtype('int64'), 'forecast_count': dtype('float64'), 'min_target_date': dtype('O'), 'max_target_date': dtype('O')}
[2025-08-05 20:08:26.004] [aa95cdb4] [2343.856s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 2/6: Applying sampling if needed
[2025-08-05 20:08:26.004] [aa95cdb4] [2343.856s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 3/6: Selecting analyses to perform
[2025-08-05 20:08:26.004] [aa95cdb4] [2343.856s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Selected analyses: ['custom']
[2025-08-05 20:08:26.004] [aa95cdb4] [2343.857s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 4/6: Executing analyses
[2025-08-05 20:08:26.004] [aa95cdb4] [2343.857s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Executing 1 analysis modules: ['custom']
[2025-08-05 20:08:26.005] [aa95cdb4] [2343.857s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: [1/1] Starting analysis: custom
[2025-08-05 20:08:26.005] [aa95cdb4] [2343.857s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Options for custom: {'target_columns': None, 'analysis_goals': ['Perform forecast model optimization analysis including bias detection, forecast horizon analysis, model performance by profit center and carline, and statistical model comparison between SW and customer forecasts'], 'analysis_depth': 'comprehensive'}
[2025-08-05 20:08:45.301] [aa95cdb4] [2363.153s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ [1/1] Completed analysis: custom in 19.30s
[2025-08-05 20:08:45.301] [aa95cdb4] [2363.153s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result keys for custom: ['custom_analysis']
[2025-08-05 20:08:45.301] [aa95cdb4] [2363.153s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Analysis execution completed. Successful modules: 1
[2025-08-05 20:08:45.301] [aa95cdb4] [2363.153s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 5/6: Generating visualizations
[2025-08-05 20:08:45.301] [aa95cdb4] [2363.153s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Step 6/6: Generating recommendations
[2025-08-05 20:08:45.301] [aa95cdb4] [2363.153s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Creating final result
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.156s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: ✓ EDA analysis completed successfully in 19.31 seconds
[2025-08-05 20:08:45.304] [aa95cdb4] [2363.156s] DEBUG in eda_mcp_tool_EDAEngine - log_tool_step(): STEP: Result contains 1 analysis modules
