[2025-08-05 19:28:00.238] [aa95cdb4] [0.001s] INFO in eda_mcp_server: EDA MCP Server session started - ID: aa95cdb4
[2025-08-05 19:28:00.239] [aa95cdb4] [0.002s] INFO in eda_mcp_server: Log directory: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp\logs\session_aa95cdb4_20250805_192754
[2025-08-05 19:28:00.239] [aa95cdb4] [0.002s] INFO in eda_mcp_server: Initializing EDA MCP Server
[2025-08-05 19:28:00.240] [aa95cdb4] [0.002s] INFO in eda_mcp_server: Configuration loaded - endpoint: https://sivanithish-test.openai.azure.com/, deployment: gpt-4.1
[2025-08-05 19:28:00.240] [aa95cdb4] [0.002s] INFO in eda_mcp_server: Jina API key configured: jina_704d29e58ed547c...
[2025-08-05 19:28:00.241] [aa95cdb4] [0.004s] INFO in eda_mcp_server: Configuring Azure OpenAI client
[2025-08-05 19:28:00.965] [aa95cdb4] [0.728s] INFO in eda_mcp_server: Azure OpenAI configured with model: gpt-4.1
[2025-08-05 19:28:01.021] [aa95cdb4] [0.784s] INFO in eda_mcp_server: Starting EDA MCP server...
[2025-08-05 19:28:01.021] [aa95cdb4] [0.784s] INFO in eda_mcp_server: Version: 0.1.0
[2025-08-05 19:28:01.021] [aa95cdb4] [0.784s] INFO in eda_mcp_server: Model: gpt-4.1
[2025-08-05 19:28:01.021] [aa95cdb4] [0.784s] INFO in eda_mcp_server: Base path: Not set
[2025-08-05 19:28:01.021] [aa95cdb4] [0.784s] INFO in eda_mcp_server: Available tools: 1 tools registered
[2025-08-05 19:28:01.021] [aa95cdb4] [0.784s] INFO in eda_mcp_server: Python version: 3.13.2 (main, Mar 11 2025, 17:20:07) [MSC v.1943 64 bit (AMD64)]
[2025-08-05 19:28:01.022] [aa95cdb4] [0.784s] INFO in eda_mcp_server: Working directory: C:\Users\<USER>\Desktop\Code\AI\Lida-MCP\lida-mcp
[2025-08-05 19:28:01.022] [aa95cdb4] [0.784s] INFO in eda_mcp_server: AZURE_OPENAI_ENDPOINT: https://sivanithish-...
[2025-08-05 19:28:01.022] [aa95cdb4] [0.784s] INFO in eda_mcp_server: AZURE_OPENAI_DEPLOYMENT_NAME: gpt-4.1...
[2025-08-05 19:28:01.022] [aa95cdb4] [0.784s] INFO in eda_mcp_server: EDA_MCP_BASE_PATH: Not set
[2025-08-05 19:28:01.022] [aa95cdb4] [0.784s] INFO in eda_mcp_server: EDA MCP Server initialization complete
[2025-08-05 19:28:01.022] [aa95cdb4] [0.784s] INFO in eda_mcp_server: ============================================================
[2025-08-05 19:29:22.048] [aa95cdb4] [81.811s] INFO in eda_mcp_server: Starting tool: perform_eda
[2025-08-05 19:29:47.346] [aa95cdb4] [107.108s] INFO in eda_mcp_server: Tool completed successfully: perform_eda
[2025-08-05 19:29:52.388] [aa95cdb4] [112.151s] INFO in eda_mcp_server: Starting tool: perform_eda
[2025-08-05 19:33:52.135] [aa95cdb4] [351.898s] INFO in eda_mcp_server: Tool completed successfully: perform_eda
[2025-08-05 19:33:52.185] [aa95cdb4] [351.948s] INFO in eda_mcp_server: Starting tool: perform_eda
[2025-08-05 19:34:23.553] [aa95cdb4] [383.315s] INFO in eda_mcp_server: Tool completed successfully: perform_eda
[2025-08-05 19:34:23.576] [aa95cdb4] [383.338s] INFO in eda_mcp_server: Starting tool: perform_eda
[2025-08-05 19:34:36.457] [aa95cdb4] [396.220s] INFO in eda_mcp_server: Tool completed successfully: perform_eda
[2025-08-05 20:07:58.808] [aa95cdb4] [2398.572s] INFO in eda_mcp_server: Starting tool: perform_eda
[2025-08-05 20:08:20.089] [aa95cdb4] [2419.851s] INFO in eda_mcp_server: Tool completed successfully: perform_eda
[2025-08-05 20:08:25.996] [aa95cdb4] [2425.759s] INFO in eda_mcp_server: Starting tool: perform_eda
[2025-08-05 20:08:45.305] [aa95cdb4] [2445.067s] INFO in eda_mcp_server: Tool completed successfully: perform_eda
