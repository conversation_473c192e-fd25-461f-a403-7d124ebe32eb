"""
EDA Engine and Analysis Modules

This module contains the core EDA engine and various analysis modules
for performing exploratory data analysis on datasets.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import time
import warnings
from pathlib import Path
import json
import sys
import io
import contextlib
import traceback

from .model import (
    EDARequest, EDAResult, DatasetInfo, BasicStats, DataQuality,
    Relationships, Patterns, Recommendations, Visualizations,
    EDAAnalysisModule
)
from .utils import (
    EDAError, EDADataError, EDAAnalysisError, EDATimeoutError,
    load_data_file, make_output_path, make_output_file
)
from .logging_config import get_logger, log_step, log_data_op

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

logger = get_logger()


class EDAEngine:
    """Main EDA engine that orchestrates the analysis process"""
    
    def __init__(self):
        self.analysis_modules = {}
        self.results = {}
        self.execution_start_time = None
        
    def register_module(self, module: EDAAnalysisModule):
        """Register an analysis module"""
        self.analysis_modules[module.name] = module
        log_step("EDAEngine", f"Registered analysis module: {module.name}")
    
    def perform_analysis(self, request: EDARequest) -> EDAResult:
        """Main entry point for performing EDA analysis"""
        self.execution_start_time = time.time()
        log_step("EDAEngine", f"Starting EDA analysis: {request.analysis_type}")
        log_step("EDAEngine", f"Request details - File: {request.file_path}, Goals: {request.analysis_goals}")
        
        try:
            # Load and validate data
            log_step("EDAEngine", "Step 1/6: Loading and validating data")
            df = self._load_and_validate_data(request)
            
            # Apply sampling if needed
            log_step("EDAEngine", "Step 2/6: Applying sampling if needed")
            df = self._apply_sampling(df, request)
            
            # Select analyses to perform
            log_step("EDAEngine", "Step 3/6: Selecting analyses to perform")
            selected_analyses = self._select_analyses(request)
            
            # Execute analyses
            log_step("EDAEngine", "Step 4/6: Executing analyses")
            analysis_results = self._execute_analyses(df, selected_analyses, request)
            
            # Generate visualizations if requested
            log_step("EDAEngine", "Step 5/6: Generating visualizations")
            visualizations = self._generate_visualizations(df, analysis_results, request)
            
            # Generate recommendations
            log_step("EDAEngine", "Step 6/6: Generating recommendations")
            recommendations = self._generate_recommendations(analysis_results, request)
            
            # Create final result
            log_step("EDAEngine", "Creating final result")
            result = self._create_result(df, analysis_results, visualizations, recommendations, request)
            
            execution_time = time.time() - self.execution_start_time
            result.execution_time = execution_time
            
            log_step("EDAEngine", f"✓ EDA analysis completed successfully in {execution_time:.2f} seconds")
            log_step("EDAEngine", f"Result contains {len(analysis_results)} analysis modules")
            return result
            
        except Exception as e:
            execution_time = time.time() - self.execution_start_time if self.execution_start_time else 0
            log_step("EDAEngine", f"✗ EDA analysis failed after {execution_time:.2f} seconds: {str(e)}")
            logger.error(f"EDA analysis error details: {traceback.format_exc()}")
            raise EDAError(f"EDA analysis failed: {str(e)}")
    
    def _load_and_validate_data(self, request: EDARequest) -> pd.DataFrame:
        """Load and validate the dataset"""
        log_step("EDAEngine", f"Loading data from: {request.file_path}")
        
        try:
            file_path = Path(request.file_path)
            if not file_path.exists():
                log_step("EDAEngine", f"✗ File not found: {request.file_path}")
                raise EDADataError(f"File not found: {request.file_path}")
            
            log_step("EDAEngine", f"File exists, size: {file_path.stat().st_size / (1024 * 1024):.2f} MB")
            df = load_data_file(file_path)
            
            if df.empty:
                log_step("EDAEngine", "✗ Dataset is empty")
                raise EDADataError("Dataset is empty")
            
            log_data_op("EDAEngine", "data_loaded", {
                "rows": len(df),
                "columns": len(df.columns),
                "file_size_mb": file_path.stat().st_size / (1024 * 1024)
            })
            
            log_step("EDAEngine", f"✓ Data loaded successfully: {len(df)} rows, {len(df.columns)} columns")
            log_step("EDAEngine", f"Columns: {list(df.columns)}")
            log_step("EDAEngine", f"Data types: {dict(df.dtypes)}")
            
            return df
            
        except Exception as e:
            log_step("EDAEngine", f"✗ Failed to load data: {str(e)}")
            logger.error(f"Data loading error details: {traceback.format_exc()}")
            raise EDADataError(f"Failed to load data: {str(e)}")
    
    def _apply_sampling(self, df: pd.DataFrame, request: EDARequest) -> pd.DataFrame:
        """Apply intelligent sampling for large datasets"""
        if request.sample_size and len(df) > request.sample_size:
            log_step("EDAEngine", f"Sampling dataset from {len(df)} to {request.sample_size} rows")
            
            # Use stratified sampling if possible
            if len(df.select_dtypes(include=['object']).columns) > 0:
                # Try stratified sampling on the first categorical column
                cat_col = df.select_dtypes(include=['object']).columns[0]
                try:
                    df_sampled = df.groupby(cat_col, group_keys=False).apply(
                        lambda x: x.sample(min(len(x), request.sample_size // df[cat_col].nunique()))
                    ).reset_index(drop=True)
                    
                    if len(df_sampled) < request.sample_size:
                        # Fill remaining with random sampling
                        remaining = request.sample_size - len(df_sampled)
                        additional = df.drop(df_sampled.index).sample(min(remaining, len(df) - len(df_sampled)))
                        df_sampled = pd.concat([df_sampled, additional]).reset_index(drop=True)
                    
                    return df_sampled[:request.sample_size]
                except:
                    # Fall back to random sampling
                    pass
            
            # Random sampling
            return df.sample(n=request.sample_size, random_state=42).reset_index(drop=True)
        
        return df
    
    def _select_analyses(self, request: EDARequest) -> List[str]:
        """Select which analyses to perform based on request"""
        analyses = []

        if request.analysis_type == "custom":
            # For custom analysis, use only the custom module
            analyses.append("custom")
        else:
            # For basic and advanced analysis, use traditional modules
            if request.analysis_type in ["basic", "advanced"]:
                analyses.append("basic")
                analyses.append("data_quality")

            if request.analysis_type == "advanced":
                analyses.append("advanced")
                analyses.append("patterns")

        # Filter by available modules
        available_analyses = [name for name in analyses if name in self.analysis_modules]

        log_step("EDAEngine", f"Selected analyses: {available_analyses}")
        return available_analyses
    
    def _execute_analyses(self, df: pd.DataFrame, analyses: List[str], request: EDARequest) -> Dict[str, Any]:
        """Execute the selected analyses"""
        results = {}
        log_step("EDAEngine", f"Executing {len(analyses)} analysis modules: {analyses}")
        
        for i, analysis_name in enumerate(analyses, 1):
            if analysis_name not in self.analysis_modules:
                log_step("EDAEngine", f"⚠ Warning: Analysis module '{analysis_name}' not found")
                continue
            
            # Check timeout
            if request.max_execution_time:
                elapsed = time.time() - self.execution_start_time
                if elapsed > request.max_execution_time:
                    log_step("EDAEngine", f"✗ Timeout after {elapsed:.2f} seconds")
                    raise EDATimeoutError(f"Analysis timeout after {elapsed:.2f} seconds")
            
            try:
                log_step("EDAEngine", f"[{i}/{len(analyses)}] Starting analysis: {analysis_name}")
                module_start_time = time.time()
                module = self.analysis_modules[analysis_name]
                
                # Prepare options for the module
                options = {
                    "target_columns": request.target_columns,
                    "analysis_goals": request.analysis_goals,
                    "analysis_depth": request.analysis_depth
                }
                
                log_step("EDAEngine", f"Options for {analysis_name}: {options}")
                
                result = module.analyze(df, options)
                module_time = time.time() - module_start_time
                results[analysis_name] = result
                
                log_step("EDAEngine", f"✓ [{i}/{len(analyses)}] Completed analysis: {analysis_name} in {module_time:.2f}s")
                
                # Log result summary
                if isinstance(result, dict):
                    log_step("EDAEngine", f"Result keys for {analysis_name}: {list(result.keys())}")
                
            except Exception as e:
                module_time = time.time() - module_start_time if 'module_start_time' in locals() else 0
                error_msg = f"Analysis '{analysis_name}' failed after {module_time:.2f}s: {str(e)}"
                log_step("EDAEngine", f"✗ [{i}/{len(analyses)}] {error_msg}")
                logger.error(f"Analysis module error details for {analysis_name}: {traceback.format_exc()}")
                results[analysis_name] = {"error": str(e), "execution_time": module_time}
        
        log_step("EDAEngine", f"Analysis execution completed. Successful modules: {len([k for k, v in results.items() if 'error' not in v])}")
        return results
    
    def _generate_visualizations(self, df: pd.DataFrame, analysis_results: Dict[str, Any], request: EDARequest) -> Visualizations:
        """Generate visualizations based on analysis results"""
        if not request.include_visualizations:
            return Visualizations(
                generated_charts=[],
                chart_paths=[],
                chart_descriptions={}
            )
        
        # This will be implemented when we add the visualization module
        log_step("EDAEngine", "Visualization generation not yet implemented")
        return Visualizations(
            generated_charts=[],
            chart_paths=[],
            chart_descriptions={}
        )
    
    def _generate_recommendations(self, analysis_results: Dict[str, Any], request: EDARequest) -> Recommendations:
        """Generate recommendations based on analysis results"""
        all_recommendations = {
            "next_steps": [],
            "data_cleaning": [],
            "analysis_suggestions": [],
            "priority_actions": []
        }
        
        # Collect recommendations from all modules
        for analysis_name, result in analysis_results.items():
            if analysis_name in self.analysis_modules and "error" not in result:
                try:
                    module = self.analysis_modules[analysis_name]
                    module_recommendations = module.get_recommendations(result)
                    
                    for rec in module_recommendations:
                        category = rec.get("category", "analysis_suggestions")
                        if category in all_recommendations:
                            all_recommendations[category].append(rec)
                
                except Exception as e:
                    log_step("EDAEngine", f"Failed to get recommendations from {analysis_name}: {str(e)}")
        
        return Recommendations(**all_recommendations)
    
    def _create_result(self, df: pd.DataFrame, analysis_results: Dict[str, Any], 
                      visualizations: Visualizations, recommendations: Recommendations,
                      request: EDARequest) -> EDAResult:
        """Create the final EDA result"""
        
        # Create dataset info
        dataset_info = DatasetInfo(
            name=Path(request.file_path).stem,
            shape=[len(df), len(df.columns)],
            size_mb=df.memory_usage(deep=True).sum() / (1024 * 1024),
            columns=df.columns.tolist(),
            dtypes={col: str(dtype) for col, dtype in df.dtypes.items()},
            memory_usage=df.memory_usage(deep=True).to_dict()
        )
        
        # Extract results from analysis modules
        basic_stats = BasicStats(
            numerical={},
            categorical={},
            summary_stats={}
        )
        
        data_quality = DataQuality(
            missing_values={},
            duplicates={},
            outliers={},
            issues=[],
            quality_score=0.0
        )
        
        relationships = Relationships(
            correlations={},
            associations={},
            feature_interactions=[]
        )
        
        patterns = Patterns(
            trends=[],
            clusters=[],
            anomalies=[],
            seasonality=None
        )
        
        # Populate from analysis results
        if "basic" in analysis_results:
            basic_result = analysis_results["basic"]
            basic_stats = BasicStats(
                numerical=basic_result.get("numerical", {}),
                categorical=basic_result.get("categorical", {}),
                summary_stats=basic_result.get("summary_stats", {})
            )
        
        if "data_quality" in analysis_results:
            quality_result = analysis_results["data_quality"]
            data_quality = DataQuality(
                missing_values=quality_result.get("missing_values", {}),
                duplicates=quality_result.get("duplicates", {}),
                outliers=quality_result.get("outliers", {}),
                issues=quality_result.get("issues", []),
                quality_score=quality_result.get("quality_score", 0.0)
            )
        
        if "advanced" in analysis_results:
            advanced_result = analysis_results["advanced"]
            relationships = Relationships(
                correlations=advanced_result.get("correlations", {}),
                associations=advanced_result.get("associations", {}),
                feature_interactions=advanced_result.get("feature_interactions", [])
            )
        
        if "patterns" in analysis_results:
            pattern_result = analysis_results["patterns"]
            patterns = Patterns(
                trends=pattern_result.get("trends", []),
                clusters=pattern_result.get("clusters", []),
                anomalies=pattern_result.get("anomalies", []),
                seasonality=pattern_result.get("seasonality")
            )
        
        # Create analysis metadata
        analysis_metadata = {
            "request": request.dict(),
            "timestamp": datetime.now().isoformat(),
            "modules_executed": list(analysis_results.keys()),
            "errors": [name for name, result in analysis_results.items() if "error" in result]
        }
        
        result = EDAResult(
            dataset_info=dataset_info,
            basic_stats=basic_stats,
            data_quality=data_quality,
            relationships=relationships,
            patterns=patterns,
            recommendations=recommendations,
            visualizations=visualizations,
            execution_time=0.0,  # Will be set by caller
            analysis_metadata=analysis_metadata
        )

        # Store raw analysis results for custom formatting
        result._raw_analysis_results = analysis_results

        return result


class BasicAnalysisModule(EDAAnalysisModule):
    """Module for basic statistical analysis"""
    
    def __init__(self):
        super().__init__("basic")
    
    def analyze(self, df: pd.DataFrame, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Perform basic statistical analysis"""
        if not self.validate_data(df):
            raise EDAAnalysisError("Invalid data for basic analysis")
        
        options = options or {}
        target_columns = options.get("target_columns")
        
        # Filter columns if specified
        if target_columns:
            available_cols = [col for col in target_columns if col in df.columns]
            if available_cols:
                df = df[available_cols]
        
        results = {
            "numerical": self._analyze_numerical(df),
            "categorical": self._analyze_categorical(df),
            "summary_stats": self._get_summary_stats(df)
        }
        
        return results
    
    def _analyze_numerical(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze numerical columns"""
        numerical_cols = df.select_dtypes(include=[np.number]).columns
        numerical_analysis = {}
        
        for col in numerical_cols:
            series = df[col].dropna()
            if len(series) == 0:
                continue
            
            analysis = {
                "count": len(series),
                "mean": float(series.mean()),
                "median": float(series.median()),
                "std": float(series.std()),
                "min": float(series.min()),
                "max": float(series.max()),
                "q25": float(series.quantile(0.25)),
                "q75": float(series.quantile(0.75)),
                "skewness": float(series.skew()),
                "kurtosis": float(series.kurtosis()),
                "missing_count": int(df[col].isna().sum()),
                "missing_percentage": float(df[col].isna().sum() / len(df) * 100),
                "unique_count": int(series.nunique()),
                "zero_count": int((series == 0).sum()),
                "negative_count": int((series < 0).sum())
            }
            
            # Add distribution characteristics
            if analysis["std"] > 0:
                analysis["coefficient_of_variation"] = analysis["std"] / abs(analysis["mean"]) if analysis["mean"] != 0 else float('inf')
            else:
                analysis["coefficient_of_variation"] = 0.0
            
            # Detect potential data types
            if series.nunique() <= 10 and series.dtype in ['int64', 'float64']:
                analysis["potential_categorical"] = True
            else:
                analysis["potential_categorical"] = False
            
            numerical_analysis[col] = analysis
        
        return numerical_analysis
    
    def _analyze_categorical(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze categorical columns"""
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        categorical_analysis = {}
        
        for col in categorical_cols:
            series = df[col].dropna()
            if len(series) == 0:
                continue
            
            value_counts = series.value_counts()
            
            analysis = {
                "count": len(series),
                "unique_count": int(series.nunique()),
                "missing_count": int(df[col].isna().sum()),
                "missing_percentage": float(df[col].isna().sum() / len(df) * 100),
                "most_frequent": str(value_counts.index[0]) if len(value_counts) > 0 else None,
                "most_frequent_count": int(value_counts.iloc[0]) if len(value_counts) > 0 else 0,
                "least_frequent": str(value_counts.index[-1]) if len(value_counts) > 0 else None,
                "least_frequent_count": int(value_counts.iloc[-1]) if len(value_counts) > 0 else 0,
                "top_5_values": value_counts.head(5).to_dict(),
                "cardinality_ratio": float(series.nunique() / len(series)),
                "entropy": float(-sum((p := value_counts / len(series)) * np.log2(p + 1e-10)))
            }
            
            # Detect high cardinality
            if analysis["cardinality_ratio"] > 0.9:
                analysis["high_cardinality"] = True
            else:
                analysis["high_cardinality"] = False
            
            # Detect potential issues
            issues = []
            if analysis["missing_percentage"] > 50:
                issues.append("high_missing_rate")
            if analysis["unique_count"] == 1:
                issues.append("constant_value")
            if analysis["cardinality_ratio"] > 0.95:
                issues.append("very_high_cardinality")
            
            analysis["issues"] = issues
            
            categorical_analysis[col] = analysis
        
        return categorical_analysis
    
    def _get_summary_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Get overall dataset summary statistics"""
        return {
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "numerical_columns": len(df.select_dtypes(include=[np.number]).columns),
            "categorical_columns": len(df.select_dtypes(include=['object', 'category']).columns),
            "datetime_columns": len(df.select_dtypes(include=['datetime64']).columns),
            "total_missing_values": int(df.isna().sum().sum()),
            "missing_percentage": float(df.isna().sum().sum() / (len(df) * len(df.columns)) * 100),
            "duplicate_rows": int(df.duplicated().sum()),
            "duplicate_percentage": float(df.duplicated().sum() / len(df) * 100),
            "memory_usage_mb": float(df.memory_usage(deep=True).sum() / (1024 * 1024))
        }
    
    def get_recommendations(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate recommendations based on basic analysis results"""
        recommendations = []
        
        # Check numerical columns for issues
        for col, analysis in results.get("numerical", {}).items():
            if analysis["missing_percentage"] > 20:
                recommendations.append({
                    "category": "data_cleaning",
                    "priority": "high",
                    "action": f"Handle missing values in '{col}' ({analysis['missing_percentage']:.1f}% missing)",
                    "details": "Consider imputation, removal, or separate analysis of missing patterns"
                })
            
            if analysis["potential_categorical"]:
                recommendations.append({
                    "category": "analysis_suggestions",
                    "priority": "medium",
                    "action": f"Consider treating '{col}' as categorical",
                    "details": f"Column has only {analysis['unique_count']} unique values"
                })
            
            if abs(analysis["skewness"]) > 2:
                recommendations.append({
                    "category": "analysis_suggestions",
                    "priority": "medium",
                    "action": f"Consider transformation for '{col}' (highly skewed)",
                    "details": f"Skewness: {analysis['skewness']:.2f}"
                })
        
        # Check categorical columns for issues
        for col, analysis in results.get("categorical", {}).items():
            if "high_missing_rate" in analysis["issues"]:
                recommendations.append({
                    "category": "data_cleaning",
                    "priority": "high",
                    "action": f"Investigate high missing rate in '{col}'",
                    "details": f"{analysis['missing_percentage']:.1f}% of values are missing"
                })
            
            if "very_high_cardinality" in analysis["issues"]:
                recommendations.append({
                    "category": "analysis_suggestions",
                    "priority": "medium",
                    "action": f"Consider grouping categories in '{col}'",
                    "details": f"Very high cardinality: {analysis['unique_count']} unique values"
                })
            
            if "constant_value" in analysis["issues"]:
                recommendations.append({
                    "category": "data_cleaning",
                    "priority": "low",
                    "action": f"Consider removing constant column '{col}'",
                    "details": "Column has only one unique value"
                })
        
        # Overall dataset recommendations
        summary = results.get("summary_stats", {})
        if summary.get("missing_percentage", 0) > 30:
            recommendations.append({
                "category": "priority_actions",
                "priority": "high",
                "action": "Address overall data quality issues",
                "details": f"Dataset has {summary['missing_percentage']:.1f}% missing values overall"
            })
        
        if summary.get("duplicate_percentage", 0) > 5:
            recommendations.append({
                "category": "data_cleaning",
                "priority": "medium",
                "action": "Investigate duplicate rows",
                "details": f"{summary['duplicate_percentage']:.1f}% of rows are duplicates"
            })
        
        # Next steps recommendations
        recommendations.append({
            "category": "next_steps",
            "priority": "medium",
            "action": "Perform correlation analysis",
            "details": "Analyze relationships between numerical variables"
        })
        
        if results.get("categorical"):
            recommendations.append({
                "category": "next_steps",
                "priority": "medium",
                "action": "Analyze categorical associations",
                "details": "Examine relationships between categorical variables"
            })
        
        return recommendations


class DataQualityModule(EDAAnalysisModule):
    """Module for data quality analysis"""
    
    def __init__(self):
        super().__init__("data_quality")
    
    def analyze(self, df: pd.DataFrame, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Perform data quality analysis"""
        if not self.validate_data(df):
            raise EDAAnalysisError("Invalid data for data quality analysis")
        
        options = options or {}
        target_columns = options.get("target_columns")
        
        # Filter columns if specified
        if target_columns:
            available_cols = [col for col in target_columns if col in df.columns]
            if available_cols:
                df = df[available_cols]
        
        results = {
            "missing_values": self._analyze_missing_values(df),
            "duplicates": self._analyze_duplicates(df),
            "outliers": self._analyze_outliers(df),
            "issues": self._identify_issues(df),
            "quality_score": self._calculate_quality_score(df)
        }
        
        return results
    
    def _analyze_missing_values(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze missing value patterns"""
        missing_analysis = {}
        
        # Overall missing statistics
        total_missing = df.isna().sum().sum()
        total_cells = len(df) * len(df.columns)
        
        missing_analysis["total_missing"] = int(total_missing)
        missing_analysis["total_cells"] = int(total_cells)
        missing_analysis["missing_percentage"] = float(total_missing / total_cells * 100)
        
        # Per-column missing analysis
        column_missing = {}
        for col in df.columns:
            missing_count = df[col].isna().sum()
            column_missing[col] = {
                "count": int(missing_count),
                "percentage": float(missing_count / len(df) * 100)
            }
        
        missing_analysis["by_column"] = column_missing
        
        # Missing value patterns
        if total_missing > 0:
            # Find rows with missing values
            rows_with_missing = df.isna().any(axis=1).sum()
            missing_analysis["rows_with_missing"] = int(rows_with_missing)
            missing_analysis["rows_with_missing_percentage"] = float(rows_with_missing / len(df) * 100)
            
            # Find columns with missing values
            cols_with_missing = df.isna().any(axis=0).sum()
            missing_analysis["columns_with_missing"] = int(cols_with_missing)
            missing_analysis["columns_with_missing_percentage"] = float(cols_with_missing / len(df.columns) * 100)
            
            # Missing patterns (combinations of missing columns)
            missing_patterns = df.isna().value_counts().head(10)
            missing_analysis["common_patterns"] = {
                str(pattern): int(count) for pattern, count in missing_patterns.items()
            }
        
        return missing_analysis
    
    def _analyze_duplicates(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze duplicate rows and values"""
        duplicate_analysis = {}
        
        # Duplicate rows
        duplicate_rows = df.duplicated()
        duplicate_analysis["duplicate_rows"] = {
            "count": int(duplicate_rows.sum()),
            "percentage": float(duplicate_rows.sum() / len(df) * 100)
        }
        
        # Duplicate analysis by column
        column_duplicates = {}
        for col in df.columns:
            if df[col].dtype in ['object', 'category']:
                # For categorical columns, analyze value duplicates
                value_counts = df[col].value_counts()
                duplicated_values = value_counts[value_counts > 1]
                
                column_duplicates[col] = {
                    "unique_values": int(df[col].nunique()),
                    "duplicated_values": int(len(duplicated_values)),
                    "most_duplicated": {
                        "value": str(value_counts.index[0]) if len(value_counts) > 0 else None,
                        "count": int(value_counts.iloc[0]) if len(value_counts) > 0 else 0
                    }
                }
            else:
                # For numerical columns, check for exact duplicates
                duplicated_count = df[col].duplicated().sum()
                column_duplicates[col] = {
                    "exact_duplicates": int(duplicated_count),
                    "unique_values": int(df[col].nunique())
                }
        
        duplicate_analysis["by_column"] = column_duplicates
        
        return duplicate_analysis
    
    def _analyze_outliers(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze outliers in numerical columns"""
        outlier_analysis = {}
        numerical_cols = df.select_dtypes(include=[np.number]).columns
        
        for col in numerical_cols:
            series = df[col].dropna()
            if len(series) < 4:  # Need at least 4 values for outlier detection
                continue
            
            col_outliers = {}
            
            # IQR method
            Q1 = series.quantile(0.25)
            Q3 = series.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            iqr_outliers = series[(series < lower_bound) | (series > upper_bound)]
            col_outliers["iqr"] = {
                "count": len(iqr_outliers),
                "percentage": float(len(iqr_outliers) / len(series) * 100),
                "lower_bound": float(lower_bound),
                "upper_bound": float(upper_bound),
                "outlier_values": iqr_outliers.tolist()[:10]  # Limit to first 10
            }
            
            # Z-score method (if std > 0)
            if series.std() > 0:
                z_scores = np.abs((series - series.mean()) / series.std())
                z_outliers = series[z_scores > 3]
                col_outliers["zscore"] = {
                    "count": len(z_outliers),
                    "percentage": float(len(z_outliers) / len(series) * 100),
                    "threshold": 3.0,
                    "outlier_values": z_outliers.tolist()[:10]
                }
            
            outlier_analysis[col] = col_outliers
        
        return outlier_analysis
    
    def _identify_issues(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Identify data quality issues"""
        issues = []
        
        # Check for high missing rates
        for col in df.columns:
            missing_pct = df[col].isna().sum() / len(df) * 100
            if missing_pct > 50:
                issues.append({
                    "type": "high_missing_rate",
                    "severity": "high",
                    "column": col,
                    "description": f"Column '{col}' has {missing_pct:.1f}% missing values",
                    "recommendation": "Consider imputation or removal"
                })
            elif missing_pct > 20:
                issues.append({
                    "type": "moderate_missing_rate",
                    "severity": "medium",
                    "column": col,
                    "description": f"Column '{col}' has {missing_pct:.1f}% missing values",
                    "recommendation": "Investigate missing patterns"
                })
        
        # Check for constant columns
        for col in df.columns:
            if df[col].nunique() <= 1:
                issues.append({
                    "type": "constant_column",
                    "severity": "low",
                    "column": col,
                    "description": f"Column '{col}' has constant values",
                    "recommendation": "Consider removing this column"
                })
        
        # Check for high cardinality categorical columns
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        for col in categorical_cols:
            cardinality_ratio = df[col].nunique() / len(df)
            if cardinality_ratio > 0.9:
                issues.append({
                    "type": "high_cardinality",
                    "severity": "medium",
                    "column": col,
                    "description": f"Column '{col}' has very high cardinality ({df[col].nunique()} unique values)",
                    "recommendation": "Consider grouping or encoding strategies"
                })
        
        # Check for duplicate rows
        duplicate_pct = df.duplicated().sum() / len(df) * 100
        if duplicate_pct > 10:
            issues.append({
                "type": "high_duplicate_rate",
                "severity": "high",
                "column": None,
                "description": f"Dataset has {duplicate_pct:.1f}% duplicate rows",
                "recommendation": "Investigate and remove duplicates"
            })
        elif duplicate_pct > 5:
            issues.append({
                "type": "moderate_duplicate_rate",
                "severity": "medium",
                "column": None,
                "description": f"Dataset has {duplicate_pct:.1f}% duplicate rows",
                "recommendation": "Review duplicate patterns"
            })
        
        return issues
    
    def _calculate_quality_score(self, df: pd.DataFrame) -> float:
        """Calculate overall data quality score (0-100)"""
        score = 100.0
        
        # Penalize for missing values
        missing_pct = df.isna().sum().sum() / (len(df) * len(df.columns)) * 100
        score -= min(missing_pct, 50)  # Max penalty of 50 points
        
        # Penalize for duplicates
        duplicate_pct = df.duplicated().sum() / len(df) * 100
        score -= min(duplicate_pct * 2, 20)  # Max penalty of 20 points
        
        # Penalize for constant columns
        constant_cols = sum(1 for col in df.columns if df[col].nunique() <= 1)
        score -= min(constant_cols * 5, 15)  # Max penalty of 15 points
        
        # Penalize for very high cardinality categorical columns
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        high_cardinality_cols = sum(1 for col in categorical_cols if df[col].nunique() / len(df) > 0.9)
        score -= min(high_cardinality_cols * 3, 10)  # Max penalty of 10 points
        
        return max(score, 0.0)
    
    def get_recommendations(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate recommendations based on data quality analysis"""
        recommendations = []
        
        # Add recommendations based on identified issues
        for issue in results.get("issues", []):
            recommendations.append({
                "category": "data_cleaning",
                "priority": issue["severity"],
                "action": issue["recommendation"],
                "details": issue["description"]
            })
        
        # Quality score based recommendations
        quality_score = results.get("quality_score", 100)
        if quality_score < 50:
            recommendations.append({
                "category": "priority_actions",
                "priority": "high",
                "action": "Comprehensive data cleaning required",
                "details": f"Data quality score is {quality_score:.1f}/100"
            })
        elif quality_score < 75:
            recommendations.append({
                "category": "data_cleaning",
                "priority": "medium",
                "action": "Address data quality issues",
                "details": f"Data quality score is {quality_score:.1f}/100"
            })
        
        # Missing value recommendations
        missing_info = results.get("missing_values", {})
        if missing_info.get("missing_percentage", 0) > 10:
            recommendations.append({
                "category": "next_steps",
                "priority": "high",
                "action": "Develop missing value strategy",
                "details": "Consider imputation methods or missing value analysis"
            })
        
        return recommendations


class AdvancedAnalysisModule(EDAAnalysisModule):
    """Module for advanced statistical analysis including correlations and goal-oriented analysis"""
    
    def __init__(self):
        super().__init__("advanced")
    
    def analyze(self, df: pd.DataFrame, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Perform advanced statistical analysis"""
        if not self.validate_data(df):
            raise EDAAnalysisError("Invalid data for advanced analysis")
        
        options = options or {}
        target_columns = options.get("target_columns")
        analysis_goals = options.get("analysis_goals", [])
        
        # Filter columns if specified
        if target_columns:
            available_cols = [col for col in target_columns if col in df.columns]
            if available_cols:
                df = df[available_cols]
        
        results = {
            "correlations": self._analyze_correlations(df),
            "associations": self._analyze_associations(df),
            "feature_interactions": self._analyze_feature_interactions(df),
            "goal_specific_analysis": self._perform_goal_specific_analysis(df, analysis_goals),
            "time_series_analysis": self._analyze_time_series_patterns(df),
            "statistical_tests": self._perform_statistical_tests(df)
        }
        
        return results
    
    def _analyze_correlations(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze correlations between numerical variables"""
        numerical_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numerical_cols) < 2:
            return {"message": "Insufficient numerical columns for correlation analysis"}
        
        correlation_results = {}
        
        # Pearson correlation
        pearson_corr = df[numerical_cols].corr(method='pearson')
        correlation_results["pearson"] = {
            "matrix": pearson_corr.to_dict(),
            "strong_correlations": self._find_strong_correlations(pearson_corr, threshold=0.7),
            "weak_correlations": self._find_weak_correlations(pearson_corr, threshold=0.3)
        }
        
        # Spearman correlation (rank-based)
        spearman_corr = df[numerical_cols].corr(method='spearman')
        correlation_results["spearman"] = {
            "matrix": spearman_corr.to_dict(),
            "strong_correlations": self._find_strong_correlations(spearman_corr, threshold=0.7)
        }
        
        return correlation_results
    
    def _find_strong_correlations(self, corr_matrix: pd.DataFrame, threshold: float = 0.7) -> List[Dict[str, Any]]:
        """Find strong correlations in the correlation matrix"""
        strong_corrs = []
        
        for i in range(len(corr_matrix.columns)):
            for j in range(i + 1, len(corr_matrix.columns)):
                col1, col2 = corr_matrix.columns[i], corr_matrix.columns[j]
                corr_value = corr_matrix.iloc[i, j]
                
                if abs(corr_value) >= threshold:
                    strong_corrs.append({
                        "variable1": col1,
                        "variable2": col2,
                        "correlation": float(corr_value),
                        "strength": "strong positive" if corr_value > 0 else "strong negative",
                        "interpretation": self._interpret_correlation(col1, col2, corr_value)
                    })
        
        return sorted(strong_corrs, key=lambda x: abs(x["correlation"]), reverse=True)
    
    def _find_weak_correlations(self, corr_matrix: pd.DataFrame, threshold: float = 0.3) -> List[Dict[str, Any]]:
        """Find weak correlations that might be interesting"""
        weak_corrs = []
        
        for i in range(len(corr_matrix.columns)):
            for j in range(i + 1, len(corr_matrix.columns)):
                col1, col2 = corr_matrix.columns[i], corr_matrix.columns[j]
                corr_value = corr_matrix.iloc[i, j]
                
                if 0.1 <= abs(corr_value) < threshold:
                    weak_corrs.append({
                        "variable1": col1,
                        "variable2": col2,
                        "correlation": float(corr_value),
                        "strength": "weak positive" if corr_value > 0 else "weak negative"
                    })
        
        return sorted(weak_corrs, key=lambda x: abs(x["correlation"]), reverse=True)[:10]  # Top 10
    
    def _interpret_correlation(self, col1: str, col2: str, corr_value: float) -> str:
        """Provide interpretation for correlations"""
        strength = "strong" if abs(corr_value) >= 0.7 else "moderate"
        direction = "positive" if corr_value > 0 else "negative"
        
        # Check for time series patterns
        if any(time_indicator in col1.lower() or time_indicator in col2.lower() 
               for time_indicator in ['q1', 'q2', 'q3', 'q4', 'year', 'month', 'quarter']):
            if corr_value > 0.8:
                return f"Very strong {direction} relationship - likely seasonal or temporal pattern"
            elif corr_value > 0.6:
                return f"Strong {direction} temporal relationship - consistent trend"
        
        if abs(corr_value) > 0.9:
            return f"Very strong {direction} relationship - possible redundancy or direct causation"
        elif abs(corr_value) > 0.7:
            return f"Strong {direction} relationship - significant association"
        else:
            return f"Moderate {direction} relationship"
    
    def _analyze_associations(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze associations between categorical variables"""
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        
        if len(categorical_cols) < 2:
            return {"message": "Insufficient categorical columns for association analysis"}
        
        associations = []
        
        for i, col1 in enumerate(categorical_cols):
            for col2 in categorical_cols[i+1:]:
                try:
                    # Create contingency table
                    contingency = pd.crosstab(df[col1], df[col2])
                    
                    # Calculate Cramér's V
                    chi2 = self._chi_square_test(contingency)
                    n = contingency.sum().sum()
                    cramers_v = np.sqrt(chi2 / (n * (min(contingency.shape) - 1)))
                    
                    associations.append({
                        "variable1": col1,
                        "variable2": col2,
                        "cramers_v": float(cramers_v),
                        "strength": self._interpret_cramers_v(cramers_v),
                        "contingency_table": contingency.to_dict()
                    })
                except:
                    continue
        
        return {"associations": sorted(associations, key=lambda x: x["cramers_v"], reverse=True)}
    
    def _chi_square_test(self, contingency_table: pd.DataFrame) -> float:
        """Calculate chi-square statistic"""
        from scipy.stats import chi2_contingency
        try:
            chi2, _, _, _ = chi2_contingency(contingency_table)
            return chi2
        except:
            return 0.0
    
    def _interpret_cramers_v(self, cramers_v: float) -> str:
        """Interpret Cramér's V value"""
        if cramers_v >= 0.5:
            return "strong association"
        elif cramers_v >= 0.3:
            return "moderate association"
        elif cramers_v >= 0.1:
            return "weak association"
        else:
            return "very weak association"
    
    def _analyze_feature_interactions(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Analyze potential feature interactions"""
        interactions = []
        numerical_cols = df.select_dtypes(include=[np.number]).columns
        
        # Look for multiplicative interactions
        for i, col1 in enumerate(numerical_cols):
            for col2 in numerical_cols[i+1:]:
                try:
                    interaction_term = df[col1] * df[col2]
                    
                    # Check if interaction explains additional variance
                    corr_with_others = []
                    for other_col in numerical_cols:
                        if other_col not in [col1, col2]:
                            corr = interaction_term.corr(df[other_col])
                            if not np.isnan(corr):
                                corr_with_others.append(abs(corr))
                    
                    if corr_with_others and max(corr_with_others) > 0.3:
                        interactions.append({
                            "variable1": col1,
                            "variable2": col2,
                            "interaction_type": "multiplicative",
                            "max_correlation": max(corr_with_others),
                            "interpretation": "Potential synergistic effect"
                        })
                except:
                    continue
        
        return sorted(interactions, key=lambda x: x["max_correlation"], reverse=True)[:5]
    
    def _perform_goal_specific_analysis(self, df: pd.DataFrame, analysis_goals: List[str]) -> Dict[str, Any]:
        """Perform analysis specific to user-defined goals"""
        goal_results = {}
        
        for i, goal in enumerate(analysis_goals):
            goal_key = f"goal_{i+1}"
            goal_results[goal_key] = {
                "goal": goal,
                "analysis": self._analyze_specific_goal(df, goal),
                "findings": self._generate_goal_findings(df, goal)
            }
        
        return goal_results
    
    def _analyze_specific_goal(self, df: pd.DataFrame, goal: str) -> Dict[str, Any]:
        """Analyze data based on a specific goal"""
        goal_lower = goal.lower()
        analysis = {}
        
        # Correlation analysis goals
        if "correlation" in goal_lower:
            if "quarterly" in goal_lower and "annual" in goal_lower:
                quarterly_cols = [col for col in df.columns if any(q in col.lower() for q in ['q1', 'q2', 'q3', 'q4'])]
                annual_cols = [col for col in df.columns if 'year' in col.lower() or 'annual' in col.lower()]
                
                if quarterly_cols and annual_cols:
                    # Calculate quarterly totals
                    quarterly_totals = df[quarterly_cols].sum(axis=1)
                    
                    correlations = {}
                    for annual_col in annual_cols:
                        if annual_col in df.columns:
                            corr = quarterly_totals.corr(df[annual_col])
                            correlations[annual_col] = {
                                "correlation": float(corr) if not np.isnan(corr) else 0.0,
                                "interpretation": self._interpret_quarterly_annual_correlation(corr)
                            }
                    
                    analysis["quarterly_annual_correlations"] = correlations
                    analysis["quarterly_columns_found"] = quarterly_cols
                    analysis["annual_columns_found"] = annual_cols
        
        # Outlier detection goals
        elif "outlier" in goal_lower:
            numerical_cols = df.select_dtypes(include=[np.number]).columns
            outlier_analysis = {}
            
            for col in numerical_cols:
                if "production" in goal_lower and "production" not in col.lower():
                    continue
                
                series = df[col].dropna()
                if len(series) > 0:
                    # IQR method
                    Q1, Q3 = series.quantile([0.25, 0.75])
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    
                    outliers = series[(series < lower_bound) | (series > upper_bound)]
                    
                    if len(outliers) > 0:
                        outlier_analysis[col] = {
                            "outlier_count": len(outliers),
                            "outlier_percentage": len(outliers) / len(series) * 100,
                            "outlier_values": outliers.tolist(),
                            "bounds": {"lower": float(lower_bound), "upper": float(upper_bound)},
                            "interpretation": self._interpret_production_outliers(outliers, col)
                        }
            
            analysis["outlier_detection"] = outlier_analysis
        
        # Trend analysis goals
        elif "trend" in goal_lower or "consistency" in goal_lower:
            time_cols = [col for col in df.columns if any(indicator in col.lower() 
                        for indicator in ['q1', 'q2', 'q3', 'q4', 'year', 'month'])]
            
            if time_cols:
                trend_analysis = {}
                for col in time_cols:
                    series = df[col].dropna()
                    if len(series) > 2:
                        # Calculate trend using linear regression slope
                        x = np.arange(len(series))
                        slope = np.polyfit(x, series, 1)[0]
                        
                        trend_analysis[col] = {
                            "slope": float(slope),
                            "trend_direction": "increasing" if slope > 0 else "decreasing" if slope < 0 else "stable",
                            "trend_strength": abs(slope),
                            "interpretation": self._interpret_trend_consistency(slope, col)
                        }
                
                analysis["trend_analysis"] = trend_analysis
        
        # Time series pattern analysis
        elif "time series" in goal_lower or "pattern" in goal_lower:
            time_cols = [col for col in df.columns if any(indicator in col.lower() 
                        for indicator in ['q1', 'q2', 'q3', 'q4', 'year', 'month'])]
            
            pattern_analysis = {}
            if len(time_cols) >= 4:  # Need at least 4 time periods
                # Look for seasonal patterns
                quarterly_data = {}
                for col in time_cols:
                    if any(q in col.lower() for q in ['q1', 'q2', 'q3', 'q4']):
                        quarter = next(q for q in ['q1', 'q2', 'q3', 'q4'] if q in col.lower())
                        if quarter not in quarterly_data:
                            quarterly_data[quarter] = []
                        quarterly_data[quarter].extend(df[col].dropna().tolist())
                
                if len(quarterly_data) >= 4:
                    seasonal_stats = {}
                    for quarter, values in quarterly_data.items():
                        seasonal_stats[quarter] = {
                            "mean": np.mean(values),
                            "std": np.std(values),
                            "count": len(values)
                        }
                    
                    pattern_analysis["seasonal_patterns"] = seasonal_stats
                    pattern_analysis["seasonality_detected"] = self._detect_seasonality(seasonal_stats)
            
            analysis["pattern_analysis"] = pattern_analysis
        
        # Statistical validation goals
        elif "statistical validation" in goal_lower or "integrity" in goal_lower:
            validation_results = {}
            
            # Check for data consistency
            numerical_cols = df.select_dtypes(include=[np.number]).columns
            for col in numerical_cols:
                series = df[col].dropna()
                if len(series) > 0:
                    validation_results[col] = {
                        "has_negative_values": (series < 0).any(),
                        "has_zero_values": (series == 0).any(),
                        "coefficient_of_variation": series.std() / series.mean() if series.mean() != 0 else float('inf'),
                        "data_range": {"min": float(series.min()), "max": float(series.max())},
                        "integrity_score": self._calculate_integrity_score(series)
                    }
            
            analysis["statistical_validation"] = validation_results
        
        return analysis
    
    def _interpret_quarterly_annual_correlation(self, corr: float) -> str:
        """Interpret correlation between quarterly and annual data"""
        if np.isnan(corr):
            return "Unable to calculate correlation"
        elif corr > 0.95:
            return "Excellent alignment - quarterly data strongly predicts annual totals"
        elif corr > 0.8:
            return "Good alignment - quarterly data reasonably predicts annual totals"
        elif corr > 0.6:
            return "Moderate alignment - some consistency between quarterly and annual data"
        else:
            return "Poor alignment - quarterly data may not accurately reflect annual projections"
    
    def _interpret_production_outliers(self, outliers: pd.Series, col_name: str) -> str:
        """Interpret production outliers"""
        if len(outliers) == 0:
            return "No significant outliers detected"
        
        outlier_pct = len(outliers) / len(outliers) * 100
        max_outlier = outliers.max()
        min_outlier = outliers.min()
        
        interpretation = f"Found {len(outliers)} outliers ({outlier_pct:.1f}%). "
        
        if max_outlier > 0:
            interpretation += f"Highest spike: {max_outlier:.0f} units. "
        if min_outlier < 0:
            interpretation += f"Lowest drop: {min_outlier:.0f} units. "
        
        interpretation += "These may indicate production anomalies, data entry errors, or exceptional events."
        
        return interpretation
    
    def _interpret_trend_consistency(self, slope: float, col_name: str) -> str:
        """Interpret trend consistency"""
        if abs(slope) < 0.1:
            return "Stable trend - consistent values over time"
        elif slope > 0:
            return f"Increasing trend - growing by approximately {slope:.2f} units per period"
        else:
            return f"Decreasing trend - declining by approximately {abs(slope):.2f} units per period"
    
    def _detect_seasonality(self, seasonal_stats: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
        """Detect seasonal patterns in quarterly data"""
        means = [stats["mean"] for stats in seasonal_stats.values()]
        
        if len(means) < 4:
            return {"detected": False, "reason": "Insufficient data"}
        
        # Calculate coefficient of variation across quarters
        mean_of_means = np.mean(means)
        std_of_means = np.std(means)
        cv = std_of_means / mean_of_means if mean_of_means != 0 else 0
        
        seasonality_detected = cv > 0.2  # 20% variation suggests seasonality
        
        return {
            "detected": seasonality_detected,
            "coefficient_of_variation": cv,
            "interpretation": "Strong seasonal pattern detected" if cv > 0.4 
                           else "Moderate seasonal pattern detected" if cv > 0.2 
                           else "No clear seasonal pattern"
        }
    
    def _calculate_integrity_score(self, series: pd.Series) -> float:
        """Calculate data integrity score for a series"""
        score = 100.0
        
        # Penalize for extreme outliers
        Q1, Q3 = series.quantile([0.25, 0.75])
        IQR = Q3 - Q1
        extreme_outliers = series[(series < Q1 - 3 * IQR) | (series > Q3 + 3 * IQR)]
        score -= min(len(extreme_outliers) * 5, 30)
        
        # Penalize for high coefficient of variation
        cv = series.std() / series.mean() if series.mean() != 0 else 0
        if cv > 2:
            score -= 20
        elif cv > 1:
            score -= 10
        
        # Penalize for negative values in production data
        if (series < 0).any():
            score -= 15
        
        return max(score, 0.0)
    
    def _generate_goal_findings(self, df: pd.DataFrame, goal: str) -> List[str]:
        """Generate specific findings based on the goal"""
        findings = []
        goal_lower = goal.lower()
        
        if "correlation" in goal_lower and "quarterly" in goal_lower:
            quarterly_cols = [col for col in df.columns if any(q in col.lower() for q in ['q1', 'q2', 'q3', 'q4'])]
            if quarterly_cols:
                findings.append(f"Found {len(quarterly_cols)} quarterly columns for correlation analysis")
                
                # Calculate quarterly totals and check consistency
                quarterly_totals = df[quarterly_cols].sum(axis=1)
                cv = quarterly_totals.std() / quarterly_totals.mean() if quarterly_totals.mean() != 0 else 0
                
                if cv < 0.2:
                    findings.append("Quarterly totals show consistent patterns across records")
                else:
                    findings.append("Quarterly totals show high variability - investigate potential data quality issues")
        
        elif "outlier" in goal_lower:
            numerical_cols = df.select_dtypes(include=[np.number]).columns
            total_outliers = 0
            
            for col in numerical_cols:
                series = df[col].dropna()
                if len(series) > 0:
                    Q1, Q3 = series.quantile([0.25, 0.75])
                    IQR = Q3 - Q1
                    outliers = series[(series < Q1 - 1.5 * IQR) | (series > Q3 + 1.5 * IQR)]
                    total_outliers += len(outliers)
            
            findings.append(f"Detected {total_outliers} total outliers across all numerical columns")
            
            if total_outliers > len(df) * 0.1:  # More than 10% outliers
                findings.append("High outlier rate detected - recommend detailed investigation")
        
        elif "trend" in goal_lower:
            time_cols = [col for col in df.columns if any(indicator in col.lower() 
                        for indicator in ['q1', 'q2', 'q3', 'q4', 'year'])]
            
            if time_cols:
                findings.append(f"Analyzing trends across {len(time_cols)} time-based columns")
                
                # Check for consistent trends
                trends = []
                for col in time_cols:
                    series = df[col].dropna()
                    if len(series) > 2:
                        slope = np.polyfit(range(len(series)), series, 1)[0]
                        trends.append(slope)
                
                if trends:
                    avg_trend = np.mean(trends)
                    if abs(avg_trend) < 0.1:
                        findings.append("Overall trends are stable across time periods")
                    elif avg_trend > 0:
                        findings.append("Generally increasing trends detected across time periods")
                    else:
                        findings.append("Generally decreasing trends detected across time periods")
        
        return findings
    
    def _analyze_time_series_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze time series patterns in the data"""
        time_cols = [col for col in df.columns if any(indicator in col.lower() 
                    for indicator in ['q1', 'q2', 'q3', 'q4', 'year', 'month', '2024', '2025', '2026'])]
        
        if not time_cols:
            return {"message": "No time-based columns detected"}
        
        patterns = {
            "time_columns_detected": time_cols,
            "quarterly_analysis": {},
            "yearly_analysis": {},
            "forecast_validation": {}
        }
        
        # Quarterly analysis
        quarterly_cols = [col for col in time_cols if any(q in col.lower() for q in ['q1', 'q2', 'q3', 'q4'])]
        if quarterly_cols:
            quarterly_data = {}
            for col in quarterly_cols:
                quarter_match = next((q for q in ['q1', 'q2', 'q3', 'q4'] if q in col.lower()), None)
                if quarter_match:
                    if quarter_match not in quarterly_data:
                        quarterly_data[quarter_match] = []
                    quarterly_data[quarter_match].extend(df[col].dropna().tolist())
            
            patterns["quarterly_analysis"] = {
                "quarters_found": list(quarterly_data.keys()),
                "seasonal_stats": {q: {"mean": np.mean(values), "std": np.std(values)} 
                                 for q, values in quarterly_data.items()},
                "seasonality": self._detect_seasonality({q: {"mean": np.mean(values), "std": np.std(values)} 
                                                       for q, values in quarterly_data.items()})
            }
        
        return patterns
    
    def _perform_statistical_tests(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Perform various statistical tests"""
        tests = {}
        numerical_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numerical_cols) >= 2:
            # Test for normality (Shapiro-Wilk for small samples)
            normality_tests = {}
            for col in numerical_cols[:5]:  # Limit to first 5 columns
                series = df[col].dropna()
                if 3 <= len(series) <= 5000:  # Shapiro-Wilk limitations
                    try:
                        from scipy.stats import shapiro
                        stat, p_value = shapiro(series)
                        normality_tests[col] = {
                            "statistic": float(stat),
                            "p_value": float(p_value),
                            "is_normal": p_value > 0.05,
                            "interpretation": "Data appears normally distributed" if p_value > 0.05 
                                           else "Data does not appear normally distributed"
                        }
                    except:
                        normality_tests[col] = {"error": "Could not perform normality test"}
            
            tests["normality_tests"] = normality_tests
        
        return tests
    
    def get_recommendations(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate recommendations based on advanced analysis results"""
        recommendations = []
        
        # Correlation-based recommendations
        correlations = results.get("correlations", {})
        if "pearson" in correlations:
            strong_corrs = correlations["pearson"].get("strong_correlations", [])
            if strong_corrs:
                recommendations.append({
                    "category": "analysis_suggestions",
                    "priority": "high",
                    "action": f"Investigate {len(strong_corrs)} strong correlations found",
                    "details": f"Strongest correlation: {strong_corrs[0]['variable1']} vs {strong_corrs[0]['variable2']} (r={strong_corrs[0]['correlation']:.3f})"
                })
        
        # Goal-specific recommendations
        goal_analysis = results.get("goal_specific_analysis", {})
        for goal_key, goal_data in goal_analysis.items():
            goal = goal_data.get("goal", "")
            findings = goal_data.get("findings", [])
            
            if findings:
                recommendations.append({
                    "category": "next_steps",
                    "priority": "high",
                    "action": f"Review findings for: {goal[:50]}...",
                    "details": "; ".join(findings[:2])  # First 2 findings
                })
        
        # Time series recommendations
        time_analysis = results.get("time_series_analysis", {})
        if "quarterly_analysis" in time_analysis:
            quarterly = time_analysis["quarterly_analysis"]
            if quarterly.get("seasonality", {}).get("detected"):
                recommendations.append({
                    "category": "analysis_suggestions",
                    "priority": "medium",
                    "action": "Seasonal patterns detected in quarterly data",
                    "details": "Consider seasonal adjustment or forecasting models"
                })
        
        return recommendations


class CustomAnalysisModule(EDAAnalysisModule):
    """Module for custom EDA analysis using LLM-generated code"""

    def __init__(self, text_gen=None, default_model=None):
        super().__init__("custom")
        self.text_gen = text_gen
        self.default_model = default_model
        self.analysis_type = "custom"  # Add this for compatibility

    def analyze(self, df: pd.DataFrame, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Perform custom EDA analysis using LLM-generated code"""
        log_step("CustomAnalysisModule", "Starting custom analysis module")
        
        if not self.validate_data(df):
            log_step("CustomAnalysisModule", "✗ Data validation failed")
            raise EDAAnalysisError("Invalid data for custom analysis")

        if not self.text_gen:
            log_step("CustomAnalysisModule", "✗ LLM text generator not available")
            raise EDAAnalysisError("LLM text generator not available for custom analysis")

        options = options or {}
        analysis_goals = options.get("analysis_goals", [])
        target_columns = options.get("target_columns")
        analysis_depth = options.get("analysis_depth", "standard")

        log_step("CustomAnalysisModule", f"Analysis goals: {analysis_goals}")
        log_step("CustomAnalysisModule", f"Target columns: {target_columns}")
        log_step("CustomAnalysisModule", f"Analysis depth: {analysis_depth}")

        # Ensure we only handle ONE goal for custom analysis
        if not analysis_goals:
            log_step("CustomAnalysisModule", "✗ No analysis goals provided")
            raise EDAAnalysisError("Custom analysis requires at least one analysis goal")

        # Take only the first goal to focus on one specific analysis
        primary_goal = analysis_goals[0]
        log_step("CustomAnalysisModule", f"Primary goal selected: '{primary_goal}'")

        try:
            # Generate dataset information for LLM context
            log_step("CustomAnalysisModule", "Generating dataset information for LLM context")
            dataset_info = self._generate_dataset_info(df, target_columns)
            log_step("CustomAnalysisModule", f"Dataset info generated - Shape: {dataset_info['shape']}, Columns: {len(dataset_info['columns'])}")

            # Generate custom EDA code using LLM (focused on single goal)
            log_step("CustomAnalysisModule", "Generating custom EDA code using LLM")
            generated_code = self._generate_eda_code(dataset_info, primary_goal, analysis_depth)
            log_step("CustomAnalysisModule", f"Generated code length: {len(generated_code)} characters")

            # Execute the generated code safely
            log_step("CustomAnalysisModule", "Executing generated code safely")
            execution_results = self._execute_generated_code(generated_code, df)

            # Format results
            results = {
                "custom_analysis": {
                    "generated_code": generated_code,
                    "execution_results": execution_results,
                    "dataset_info": dataset_info,
                    "analysis_goal": primary_goal,  # Single goal instead of list
                    "success": execution_results.get("success", False),
                    "error_message": execution_results.get("error_message")
                }
            }

            success_status = "✓" if results['custom_analysis']['success'] else "✗"
            log_step("CustomAnalysisModule", f"{success_status} Custom analysis completed: {results['custom_analysis']['success']}")
            
            if results['custom_analysis']['success']:
                exec_time = execution_results.get("execution_time", 0)
                log_step("CustomAnalysisModule", f"Execution time: {exec_time:.2f} seconds")
                
                # Log analysis results summary
                analysis_results = execution_results.get("analysis_results", {})
                if analysis_results:
                    log_step("CustomAnalysisModule", f"Analysis results keys: {list(analysis_results.keys())}")
            
            return results

        except Exception as e:
            error_msg = f"Custom analysis failed: {str(e)}"
            log_step("CustomAnalysisModule", f"✗ {error_msg}")
            logger.error(f"Custom analysis error details: {traceback.format_exc()}")

            # Escalate to advanced analysis mode
            log_step("CustomAnalysisModule", "Escalating to advanced analysis mode")
            return self._escalate_to_advanced_analysis(df, options, error_msg)

    def _generate_dataset_info(self, df: pd.DataFrame, target_columns: Optional[List[str]] = None) -> Dict[str, Any]:
        """Generate comprehensive dataset information for LLM context"""

        # Filter columns if specified
        analysis_df = df
        if target_columns:
            available_cols = [col for col in target_columns if col in df.columns]
            if available_cols:
                analysis_df = df[available_cols]

        # Basic dataset info
        dataset_info = {
            "shape": list(analysis_df.shape),
            "columns": list(analysis_df.columns),
            "dtypes": {col: str(dtype) for col, dtype in analysis_df.dtypes.items()},
            "memory_usage_mb": analysis_df.memory_usage(deep=True).sum() / 1024 / 1024,
        }

        # Column categorization
        numerical_cols = analysis_df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = analysis_df.select_dtypes(include=['object', 'category']).columns.tolist()
        datetime_cols = analysis_df.select_dtypes(include=['datetime64']).columns.tolist()

        dataset_info.update({
            "numerical_columns": numerical_cols,
            "categorical_columns": categorical_cols,
            "datetime_columns": datetime_cols,
            "column_count": {
                "numerical": len(numerical_cols),
                "categorical": len(categorical_cols),
                "datetime": len(datetime_cols)
            }
        })

        # Sample data for context (first few rows)
        try:
            # Create a copy for sample data and convert timestamps to strings
            sample_df = analysis_df.head(3).copy()

            # Convert datetime columns to strings to avoid JSON serialization issues
            for col in sample_df.columns:
                if (sample_df[col].dtype.name.startswith('datetime') or
                    'timestamp' in str(sample_df[col].dtype).lower() or
                    sample_df[col].dtype.name.startswith('<M8')):  # numpy datetime64
                    sample_df[col] = sample_df[col].astype(str)
                else:
                    # Handle individual date/datetime objects in object columns
                    sample_df[col] = sample_df[col].apply(
                        lambda x: str(x) if hasattr(x, 'isoformat') or hasattr(x, 'strftime') else x
                    )

            sample_data = sample_df.to_dict('records')
            dataset_info["sample_data"] = sample_data
        except Exception as e:
            dataset_info["sample_data"] = f"Unable to generate sample data: {str(e)}"

        # Basic statistics for numerical columns
        if numerical_cols:
            try:
                basic_stats = analysis_df[numerical_cols].describe().to_dict()
                # Convert any non-serializable values to strings
                for col, stats in basic_stats.items():
                    for stat_name, stat_value in stats.items():
                        if hasattr(stat_value, 'isoformat'):  # datetime-like objects
                            basic_stats[col][stat_name] = str(stat_value)
                dataset_info["basic_statistics"] = basic_stats
            except Exception as e:
                dataset_info["basic_statistics"] = f"Unable to generate basic statistics: {str(e)}"

        # Missing values info
        missing_info = analysis_df.isnull().sum().to_dict()
        dataset_info["missing_values"] = {col: int(count) for col, count in missing_info.items() if count > 0}

        return dataset_info

    def _generate_eda_code(self, dataset_info: Dict[str, Any], analysis_goal: str, analysis_depth: str) -> str:
        """Generate custom EDA code using LLM based on dataset info and single goal"""

        log_step("CustomAnalysisModule", f"Creating code generation prompt for goal: '{analysis_goal}'")
        
        # Create comprehensive prompt for code generation
        prompt = self._create_code_generation_prompt(dataset_info, analysis_goal, analysis_depth)
        log_step("CustomAnalysisModule", f"Prompt created, length: {len(prompt)} characters")

        try:
            # Import required modules for text generation
            from llmx import TextGenerationConfig

            # Configure text generation for code generation
            config = TextGenerationConfig(
                n=1,
                temperature=0.3,  # Lower temperature for more consistent code
                model=self.default_model,
                use_cache=False,
                max_tokens=6000  # Higher token limit for code generation
            )

            log_step("CustomAnalysisModule", f"Text generation config: model={self.default_model}, temp=0.3, max_tokens=6000")

            # Create message format for LLM
            messages = [
                {
                    "role": "user",
                    "content": prompt
                }
            ]

            log_step("CustomAnalysisModule", "Sending request to LLM for code generation")

            # Generate code using LLM
            response = self.text_gen.generate(messages=messages, config=config)
            log_step("CustomAnalysisModule", "Received response from LLM")

            # Extract generated code from response
            generated_code = self._extract_code_from_response(response)
            log_step("CustomAnalysisModule", f"✓ Code extraction successful, {len(generated_code)} characters generated")

            return generated_code

        except Exception as e:
            error_msg = f"Failed to generate EDA code: {str(e)}"
            log_step("CustomAnalysisModule", f"✗ {error_msg}")
            logger.error(f"Code generation error details: {traceback.format_exc()}")
            raise EDAAnalysisError(error_msg)

    def _create_code_generation_prompt(self, dataset_info: Dict[str, Any], analysis_goal: str, analysis_depth: str) -> str:
        """Create a comprehensive prompt for EDA code generation focused on single goal"""

        prompt = f"""You are an expert data scientist tasked with generating Python code for Exploratory Data Analysis (EDA).

DATASET INFORMATION:
- Shape: {dataset_info['shape']} (rows, columns)
- Columns: {', '.join(dataset_info['columns'])}
- Data Types: {json.dumps(dataset_info['dtypes'], indent=2)}
- Numerical Columns: {dataset_info['numerical_columns']}
- Categorical Columns: {dataset_info['categorical_columns']}
- DateTime Columns: {dataset_info['datetime_columns']}
- Missing Values: {json.dumps(dataset_info['missing_values'], indent=2)}

SAMPLE DATA:
{json.dumps(dataset_info.get('sample_data', []), indent=2)}

ANALYSIS GOAL:
{analysis_goal}

ANALYSIS DEPTH: {analysis_depth}

CRITICAL REQUIREMENTS:
1. Generate ONLY valid, executable Python code - NO explanations or markdown
2. Focus EXCLUSIVELY on the single analysis goal provided
3. Use pandas (pd), numpy (np), and scipy for analysis (already imported)
4. The dataframe is available as 'df' variable
5. Return results as a dictionary called 'analysis_results'
6. Do NOT include visualization code (matplotlib, seaborn, plotly, etc.)
7. Do NOT include file I/O operations
8. Ensure ALL strings are properly quoted and ALL brackets/parentheses are closed
9. Use simple, clean code structure - avoid complex nested operations
10. Include basic error handling with try/except blocks

ANALYSIS DEPTH GUIDELINES:
- quick: Focus on key metrics and basic patterns (5-10 lines of analysis code)
- standard: Include correlation analysis and goal-specific insights (10-20 lines)
- comprehensive: Add statistical tests and detailed analysis (20-40 lines)

STRICT CODE REQUIREMENTS:
- Start with: analysis_results = {{}}
- Use ONLY basic Python syntax - no complex nested structures
- Every try block MUST have a matching except block
- Every opening bracket/parenthesis MUST have a closing one
- All strings MUST be properly quoted with matching quotes
- No incomplete statements or hanging code
- End with analysis_results containing your findings

EXAMPLE TEMPLATE (FOLLOW THIS STRUCTURE EXACTLY):
```python
analysis_results = {{}}

try:
    # Simple analysis focused on: {analysis_goal}

    # Basic calculations
    total_records = len(df)
    analysis_results['total_records'] = total_records

    # Goal-specific analysis (keep it simple)
    analysis_results['findings'] = []
    analysis_results['summary'] = 'Analysis completed'

except Exception as e:
    analysis_results['error'] = str(e)
    analysis_results['success'] = False
```

Generate ONLY valid Python code following the exact structure above:"""

        return prompt

    def _extract_code_from_response(self, response) -> str:
        """Extract Python code from LLM response"""
        try:
            # Get the response text
            response_text = response.text[0]["content"]

            # Look for code blocks
            if "```python" in response_text:
                # Extract code between ```python and ```
                start_marker = "```python"
                end_marker = "```"

                start_idx = response_text.find(start_marker)
                if start_idx != -1:
                    start_idx += len(start_marker)
                    end_idx = response_text.find(end_marker, start_idx)
                    if end_idx != -1:
                        code = response_text[start_idx:end_idx].strip()
                        return code

            # If no code blocks found, try to extract code after "CODE:" or similar markers
            markers = ["CODE:", "```", "python", "# EDA Code"]
            for marker in markers:
                if marker in response_text:
                    start_idx = response_text.find(marker)
                    if start_idx != -1:
                        code = response_text[start_idx + len(marker):].strip()
                        # Clean up the code
                        lines = code.split('\n')
                        cleaned_lines = []
                        for line in lines:
                            if line.strip() and not line.strip().startswith('```'):
                                cleaned_lines.append(line)
                        if cleaned_lines:
                            return '\n'.join(cleaned_lines)

            # If still no code found, return the entire response as potential code
            return response_text.strip()

        except Exception as e:
            log_step("CustomAnalysisModule", f"Error extracting code from response: {str(e)}")
            raise EDAAnalysisError(f"Failed to extract code from LLM response: {str(e)}")

    def _execute_generated_code(self, code: str, df: pd.DataFrame) -> Dict[str, Any]:
        """Safely execute the generated EDA code"""

        log_step("CustomAnalysisModule", "Starting code execution")
        log_step("CustomAnalysisModule", f"Code to execute ({len(code)} chars):\n{code[:200]}...")

        # First, validate the code syntax
        try:
            compile(code, '<string>', 'exec')
            log_step("CustomAnalysisModule", "✓ Code syntax validation passed")
        except SyntaxError as e:
            error_msg = f"Generated code has syntax error: {str(e)}"
            log_step("CustomAnalysisModule", f"✗ Syntax validation failed: {error_msg}")
            return {
                "success": False,
                "error_message": error_msg,
                "error_traceback": str(e),
                "execution_time": 0,
                "code_executed": code
            }

        # Prepare execution environment
        log_step("CustomAnalysisModule", "Preparing execution environment")
        execution_globals = {
            'pd': pd,
            'np': np,
            'df': df.copy(),  # Work with a copy to avoid modifying original
            'json': json,
            'datetime': datetime,
            'time': time,
            'warnings': warnings,
            'analysis_results': {}  # Initialize results dictionary
        }

        # Add scipy if available
        try:
            import scipy
            import scipy.stats
            execution_globals['scipy'] = scipy
            log_step("CustomAnalysisModule", "✓ scipy added to execution environment")
        except ImportError:
            log_step("CustomAnalysisModule", "⚠ scipy not available")

        # Capture stdout and stderr
        old_stdout = sys.stdout
        old_stderr = sys.stderr
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()

        try:
            # Redirect output
            sys.stdout = stdout_capture
            sys.stderr = stderr_capture

            log_step("CustomAnalysisModule", "Executing generated code")
            
            # Execute the code with timeout
            start_time = time.time()
            exec(code, execution_globals)
            execution_time = time.time() - start_time

            # Get results
            analysis_results = execution_globals.get('analysis_results', {})
            stdout_output = stdout_capture.getvalue()
            stderr_output = stderr_capture.getvalue()

            log_step("CustomAnalysisModule", f"✓ Code executed successfully in {execution_time:.2f} seconds")
            
            if analysis_results:
                log_step("CustomAnalysisModule", f"Analysis results keys: {list(analysis_results.keys())}")
            else:
                log_step("CustomAnalysisModule", "⚠ No analysis_results found in execution")
            
            if stdout_output:
                log_step("CustomAnalysisModule", f"Stdout output ({len(stdout_output)} chars): {stdout_output[:100]}...")
            
            if stderr_output:
                log_step("CustomAnalysisModule", f"⚠ Stderr output: {stderr_output}")

            # Prepare execution results
            results = {
                "success": True,
                "analysis_results": analysis_results,
                "execution_time": execution_time,
                "stdout_output": stdout_output,
                "stderr_output": stderr_output,
                "code_executed": code
            }

            return results

        except Exception as e:
            execution_time = time.time() - start_time
            error_traceback = traceback.format_exc()
            stderr_output = stderr_capture.getvalue()

            error_msg = f"Code execution failed: {str(e)}"
            log_step("CustomAnalysisModule", f"✗ {error_msg}")
            log_step("CustomAnalysisModule", f"Error traceback: {error_traceback}")

            results = {
                "success": False,
                "error_message": error_msg,
                "error_traceback": error_traceback,
                "execution_time": execution_time,
                "stderr_output": stderr_output,
                "code_executed": code
            }

            return results

        finally:
            # Restore stdout and stderr
            sys.stdout = old_stdout
            sys.stderr = old_stderr
            log_step("CustomAnalysisModule", "Output streams restored")

    def _escalate_to_advanced_analysis(self, df: pd.DataFrame, options: Dict[str, Any], error_msg: str) -> Dict[str, Any]:
        """Escalate to advanced analysis when custom analysis fails"""

        log_step("CustomAnalysisModule", "Performing advanced analysis as fallback")

        try:
            # Create an AdvancedAnalysisModule instance for fallback
            advanced_module = AdvancedAnalysisModule()
            advanced_results = advanced_module.analyze(df, options)

            # Wrap the advanced results with custom analysis metadata
            results = {
                "custom_analysis": {
                    "generated_code": None,
                    "execution_results": {
                        "success": False,
                        "error_message": error_msg,
                        "escalated_to_advanced": True
                    },
                    "analysis_goals": options.get("analysis_goals", []),
                    "success": False,
                    "error_message": error_msg
                },
                "advanced_fallback": advanced_results
            }

            return results

        except Exception as e:
            # If even advanced analysis fails, return error information
            fallback_error = f"Both custom and advanced analysis failed. Custom error: {error_msg}. Advanced error: {str(e)}"
            log_step("CustomAnalysisModule", fallback_error)

            return {
                "custom_analysis": {
                    "generated_code": None,
                    "execution_results": {
                        "success": False,
                        "error_message": fallback_error,
                        "escalated_to_advanced": True,
                        "advanced_analysis_failed": True
                    },
                    "analysis_goals": options.get("analysis_goals", []),
                    "success": False,
                    "error_message": fallback_error
                }
            }

    def get_recommendations(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate recommendations based on custom analysis results"""
        recommendations = []

        custom_analysis = results.get("custom_analysis", {})

        if custom_analysis.get("success", False):
            # Successful custom analysis recommendations
            execution_results = custom_analysis.get("execution_results", {})
            analysis_results = execution_results.get("analysis_results", {})

            recommendations.append({
                "category": "analysis_suggestions",
                "priority": "high",
                "action": "Custom EDA analysis completed successfully",
                "details": f"Generated and executed custom analysis code with {len(analysis_results)} result categories"
            })

            # Add specific recommendations based on analysis results
            if analysis_results:
                recommendations.append({
                    "category": "next_steps",
                    "priority": "medium",
                    "action": "Review custom analysis findings",
                    "details": "Examine the generated insights and consider deeper analysis of interesting patterns"
                })

        else:
            # Failed custom analysis recommendations
            error_msg = custom_analysis.get("error_message", "Unknown error")

            if custom_analysis.get("execution_results", {}).get("escalated_to_advanced", False):
                recommendations.append({
                    "category": "analysis_suggestions",
                    "priority": "medium",
                    "action": "Custom analysis failed, used advanced analysis fallback",
                    "details": f"Error: {error_msg}. Consider refining analysis goals or checking data quality."
                })
            else:
                recommendations.append({
                    "category": "analysis_suggestions",
                    "priority": "high",
                    "action": "Custom analysis failed",
                    "details": f"Error: {error_msg}. Consider using 'advanced' analysis type instead."
                })

        return recommendations